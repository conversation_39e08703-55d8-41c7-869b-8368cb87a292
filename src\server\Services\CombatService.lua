--[[
	CombatService - 戰鬥服務
	處理玩家、寵物和怪物之間的戰鬥邏輯
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local PetConfig = require(game:GetService("ReplicatedStorage").Shared.PetConfig)

local CombatService = Knit.CreateService({
	Name = "CombatService",
	Client = {
		AttackResult = Knit.CreateSignal(),
		TakeDamage = Knit.CreateSignal(),
		CombatUpdate = Knit.CreateSignal(),
	}
})

-- 私有變量
local combatStats = {} -- 玩家戰鬥狀態

function CombatService:KnitStart()
	print("⚔️ CombatService started")
	
	-- 監聽玩家加入
	Players.PlayerAdded:Connect(function(player)
		self:_initializePlayerCombat(player)
	end)
	
	-- 監聽玩家離開
	Players.PlayerRemoving:Connect(function(player)
		combatStats[player] = nil
	end)
end

-- 初始化玩家戰鬥狀態
function CombatService:_initializePlayerCombat(player)
	combatStats[player] = {
		maxHealth = 100,
		currentHealth = 100,
		attack = 20,
		defense = 5,
		lastAttack = 0,
		attackCooldown = 1.5
	}
end

-- 客戶端請求攻擊怪物
function CombatService.Client:AttackMonster(player, instanceId)
	local currentTime = tick()
	local playerStats = combatStats[player]

	if not playerStats then
		CombatService:_initializePlayerCombat(player)
		playerStats = combatStats[player]
	end

	-- 檢查攻擊冷卻
	if currentTime - playerStats.lastAttack < playerStats.attackCooldown then
		return
	end

	-- 檢查距離
	if not CombatService:_isInAttackRange(player, instanceId) then
		return
	end

	-- 執行攻擊
	local damage = playerStats.attack
	local MonsterService = Knit.GetService("MonsterService")

	if MonsterService then
		local killed = MonsterService:DamageMonster(instanceId, damage, player)
		playerStats.lastAttack = currentTime

		-- 通知客戶端攻擊成功
		self.AttackResult:Fire(player, instanceId, damage, killed)

		print("⚔️ Player", player.Name, "attacked monster", instanceId, "for", damage, "damage")
	end
end

-- 客戶端請求寵物攻擊怪物
function CombatService.Client:PetAttackMonster(player, petId, instanceId)
	return self.Server:PetAttackMonster(player, petId, instanceId)
end

-- 移除重複的函數，邏輯已移到客戶端方法中

-- 檢查是否在攻擊範圍內
function CombatService:_isInAttackRange(player, instanceId)
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return false
	end

	local MonsterService = Knit.GetService("MonsterService")
	if not MonsterService then return false end

	local monsters = MonsterService:GetActiveMonsters()
	local monsterData = monsters[instanceId]

	if not monsterData or not monsterData.model or not monsterData.model:FindFirstChild("HumanoidRootPart") then
		return false
	end

	local playerPos = player.Character.HumanoidRootPart.Position
	local monsterPos = monsterData.model.HumanoidRootPart.Position
	local distance = (playerPos - monsterPos).Magnitude

	return distance <= 10 -- 玩家攻擊範圍10格
end

-- 處理傷害
function CombatService:DealDamage(target, damage, source, sourceId)
	if target:IsA("Player") then
		self:_damagePlayer(target, damage, source, sourceId)
	end
end

-- 玩家受到傷害
function CombatService:_damagePlayer(player, damage, source, sourceId)
	local playerStats = combatStats[player]
	if not playerStats then
		self:_initializePlayerCombat(player)
		playerStats = combatStats[player]
	end
	
	-- 計算實際傷害（考慮防禦力）
	local actualDamage = math.max(1, damage - playerStats.defense)
	playerStats.currentHealth = math.max(0, playerStats.currentHealth - actualDamage)
	
	-- 通知客戶端
	self.Client.TakeDamage:Fire(player, actualDamage, source, sourceId)
	self.Client.CombatUpdate:Fire(player, playerStats.currentHealth, playerStats.maxHealth)
	
	-- 檢查是否死亡
	if playerStats.currentHealth <= 0 then
		self:_handlePlayerDeath(player)
	end
	
	print("⚔️ Player", player.Name, "took", actualDamage, "damage from", source)
end

-- 處理玩家死亡
function CombatService:_handlePlayerDeath(player)
	-- 復活玩家（簡單實現）
	wait(2)
	local playerStats = combatStats[player]
	if playerStats then
		playerStats.currentHealth = playerStats.maxHealth
		self.Client.CombatUpdate:Fire(player, playerStats.currentHealth, playerStats.maxHealth)
	end
	
	-- 傳送到安全位置
	if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
		player.Character.HumanoidRootPart.CFrame = CFrame.new(0, 10, 0)
	end
	
	print("💀 Player", player.Name, "died and respawned")
end

-- 寵物攻擊怪物
function CombatService:PetAttackMonster(player, petId, instanceId)
	local petConfig = PetConfig.getPet(petId)
	if not petConfig then return end

	-- 檢查寵物是否在攻擊範圍內
	if not self:_isPetInAttackRange(player, petId, instanceId) then
		return
	end

	-- 計算寵物攻擊力
	local damage = petConfig.baseStats.attack or 15

	local MonsterService = Knit.GetService("MonsterService")
	if MonsterService then
		local killed = MonsterService:DamageMonster(instanceId, damage, player)

		-- 通知客戶端寵物攻擊
		self.Client.AttackResult:FireAll(instanceId, damage, killed, "pet", petId)

		print("🐾 Pet", petId, "attacked monster", instanceId, "for", damage, "damage")
	end
end

-- 檢查寵物是否在攻擊範圍內
function CombatService:_isPetInAttackRange(player, petId, instanceId)
	-- 尋找寵物模型
	local petModel = nil
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("PetId") and model:FindFirstChild("Owner") then
			local petIdValue = model:FindFirstChild("PetId")
			local ownerValue = model:FindFirstChild("Owner")

			if petIdValue.Value == petId and ownerValue.Value == player then
				petModel = model
				break
			end
		end
	end

	if not petModel or not petModel:FindFirstChild("HumanoidRootPart") then
		return false
	end

	-- 檢查怪物
	local MonsterService = Knit.GetService("MonsterService")
	if not MonsterService then return false end

	local monsters = MonsterService:GetActiveMonsters()
	local monsterData = monsters[instanceId]

	if not monsterData or not monsterData.model or not monsterData.model:FindFirstChild("HumanoidRootPart") then
		return false
	end

	local petPos = petModel.HumanoidRootPart.Position
	local monsterPos = monsterData.model.HumanoidRootPart.Position
	local distance = (petPos - monsterPos).Magnitude

	return distance <= 8 -- 寵物攻擊範圍8格
end

-- 獲取玩家戰鬥狀態
function CombatService:GetPlayerCombatStats(player)
	return combatStats[player]
end

-- 治療玩家
function CombatService:HealPlayer(player, amount)
	local playerStats = combatStats[player]
	if not playerStats then
		self:_initializePlayerCombat(player)
		playerStats = combatStats[player]
	end
	
	playerStats.currentHealth = math.min(playerStats.maxHealth, playerStats.currentHealth + amount)
	self.Client.CombatUpdate:Fire(player, playerStats.currentHealth, playerStats.maxHealth)
end

return CombatService

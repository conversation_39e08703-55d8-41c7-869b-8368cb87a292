# Bacterial Virus 寵物集成說明

## 概述
成功將 Workspace.Pet.Bacterial Virus 自定義寵物模型集成到寵物召喚圖鑑系統中，支援自定義模型召喚和戰鬥功能。

## 新增寵物資訊

### 🦠 Bacterial Virus (細菌病毒)
- **ID**: `bacterial_virus`
- **名稱**: 細菌病毒
- **種族**: 病毒
- **元素**: 毒
- **稀有度**: 傳說 (金色)
- **描述**: 來自深淵的神秘病毒生物，擁有強大的感染和腐蝕能力

### 📊 屬性數值
```lua
baseStats = {
    health = 150,   -- 生命值 (最高)
    attack = 120,   -- 攻擊力 (最高)
    defense = 80,   -- 防禦力 (中等)
    speed = 100,    -- 速度 (高)
}
```

### 🎨 外觀特色
- **主色調**: 毒綠色 `Color3.fromRGB(100, 255, 100)`
- **次色調**: 深綠色 `Color3.fromRGB(50, 200, 50)`
- **特效**: 發光效果
- **表情符號**: ☢️ (放射性符號)
- **特殊**: 使用自定義 3D 模型

### ⚡ 技能能力
- **毒液噴射**: 遠程毒素攻擊
- **病毒感染**: 持續傷害效果
- **腐蝕光環**: 範圍防禦削弱

### 🌟 特殊能力
- **毒素免疫**: 對毒系攻擊免疫
- **感染攻擊**: 攻擊可能造成持續傷害
- **自我複製**: 特殊情況下可能分裂
- **腐蝕防護**: 減少物理傷害
- **漂浮能力**: 可以在空中移動

### 📍 獲取資訊
- **來源**: 深淵實驗室
- **棲息地**: 污染的實驗環境中，以負能量為食
- **性格**: 神秘莫測，具有強烈的攻擊性，但對主人絕對忠誠
- **獲取方式**: 完成深淵實驗室的危險任務或極稀有突變
- **遭遇機率**: 1% (極稀有)
- **進化潛力**: 已是最終變異形態

## 技術實現

### 🔧 自定義模型系統
新增了自定義模型支援系統，允許使用 Workspace 中的預製模型：

```lua
-- 在 PetConfig 中標記使用自定義模型
appearance = {
    useCustomModel = true,
    -- 其他外觀配置...
}

-- PetService 自動檢測並使用自定義模型
if petConfig.appearance.useCustomModel and petId == "bacterial_virus" then
    local sourceModel = workspace.Pet:FindFirstChild("Bacterial Virus")
    if sourceModel then
        petModel = sourceModel:Clone()
        -- 設置必要組件...
    end
end
```

### 🎯 模型設置系統
新增 `_setupPetModel` 函數，確保所有寵物模型都有必要的組件：

#### 必要組件檢查
- **Humanoid**: 用於移動和動畫
- **HumanoidRootPart**: 主要控制部件
- **Owner**: 標記寵物擁有者
- **PetId**: 標記寵物類型
- **焊接約束**: 確保模型部件連接

#### 自動適配
- 檢測現有組件，避免重複創建
- 自動焊接主體部件到根部件
- 設置正確的物理屬性
- 配置移動參數

### 🎮 戰鬥系統集成
Bacterial Virus 完全集成到現有戰鬥系統：

#### 攻擊能力
- 使用配置的攻擊力 (120)
- 支援寵物自動攻擊
- 參與範圍檢測和傷害計算

#### 戰鬥特效
- 毒系攻擊動畫
- 特殊攻擊效果
- 狀態指示器

## 修改的文件

### 1. `src/shared/PetConfig.lua`
- ✅ 添加 `bacterial_virus` 寵物配置
- ✅ 新增毒元素顏色配置
- ✅ 添加 `useCustomModel` 標記

### 2. `src/server/Services/PetService.lua`
- ✅ 修改 `_createPetModel` 支援自定義模型
- ✅ 新增 `_createGeneratedPetModel` 函數
- ✅ 新增 `_setupPetModel` 通用設置函數
- ✅ 自動檢測和克隆 Workspace 模型

### 3. `src/server/Services/DataService.lua`
- ✅ 將 `bacterial_virus` 添加到默認寵物列表
- ✅ 更新寵物圖鑑發現列表

### 4. `src/client/Controllers/PetUIController.lua`
- ✅ 添加毒元素表情符號 ☢️
- ✅ 支援新寵物在圖鑑中顯示

## 使用方法

### 🎯 召喚寵物
1. **打開寵物圖鑑**: 點擊寵物UI中的"圖鑑"按鈕
2. **選擇 Bacterial Virus**: 在4×4網格中找到細菌病毒卡片
3. **查看詳情**: 點擊卡片查看完整資訊
4. **召喚**: 點擊"召喚"按鈕召喚寵物
5. **戰鬥**: 寵物會自動跟隨並攻擊怪物

### 🔍 識別特徵
- **金色稀有度標籤**: 傳說級別的金色邊框
- **毒綠色主題**: 獨特的綠色配色方案
- **☢️ 放射性符號**: 毒元素的專屬表情符號
- **最高屬性**: 在圖鑑中顯示頂級數值

### ⚔️ 戰鬥表現
- **高攻擊力**: 120點攻擊力，傷害輸出極高
- **高生命值**: 150點生命值，生存能力強
- **特殊移動**: 漂浮能力，移動更靈活
- **毒系攻擊**: 獨特的攻擊動畫和效果

## 擴展性設計

### 🔄 添加新自定義寵物
系統設計支援輕鬆添加更多自定義寵物：

```lua
-- 1. 在 PetConfig 中添加配置
["new_custom_pet"] = {
    -- 基本配置...
    appearance = {
        useCustomModel = true,
        -- 其他配置...
    },
}

-- 2. 在 PetService 中添加檢測邏輯
if petConfig.appearance.useCustomModel and petId == "new_custom_pet" then
    local sourceModel = workspace.Pet:FindFirstChild("New Custom Pet")
    -- 處理邏輯...
end
```

### 🎨 模型要求
自定義模型應該滿足以下要求：
- 放置在 `workspace.Pet` 文件夾中
- 模型名稱與配置中的檢測名稱一致
- 可選包含 Humanoid 和 HumanoidRootPart
- 所有部件應該正確命名和組織

### 🔧 自動適配
系統會自動為自定義模型添加：
- 缺失的 Humanoid 組件
- 缺失的 HumanoidRootPart
- 必要的焊接約束
- 寵物標識標籤
- 正確的物理屬性

## 測試建議

### ✅ 功能測試
1. **召喚測試**: 確認能正常召喚 Bacterial Virus
2. **模型顯示**: 檢查自定義模型是否正確顯示
3. **跟隨測試**: 驗證寵物跟隨玩家移動
4. **戰鬥測試**: 確認寵物能攻擊怪物並造成傷害
5. **收回測試**: 測試寵物收回功能

### 🎨 視覺測試
1. **圖鑑顯示**: 檢查在4×4網格中的顯示效果
2. **詳情面板**: 驗證詳細資訊顯示正確
3. **稀有度顏色**: 確認金色傳說級別顯示
4. **表情符號**: 檢查 ☢️ 符號顯示
5. **屬性條**: 驗證高數值的屬性條顯示

### ⚔️ 戰鬥測試
1. **攻擊傷害**: 確認120點攻擊力生效
2. **攻擊範圍**: 測試8格攻擊範圍
3. **攻擊動畫**: 檢查毒系攻擊特效
4. **生存能力**: 驗證150點生命值
5. **移動能力**: 測試漂浮移動效果

## 故障排除

### 🚨 常見問題

#### 模型未找到
- **問題**: 提示 "Custom model not found"
- **解決**: 確認 `workspace.Pet.Bacterial Virus` 存在
- **備用**: 系統會自動使用生成模型

#### 寵物無法移動
- **問題**: 寵物召喚後不移動
- **解決**: 檢查 HumanoidRootPart 是否正確設置
- **檢查**: 確認焊接約束是否生效

#### 戰鬥無效果
- **問題**: 寵物不攻擊怪物
- **解決**: 確認寵物有正確的 PetId 標籤
- **檢查**: 驗證戰鬥系統識別寵物

### 🔧 調試方法
```lua
-- 啟用調試信息
/debug pet on
/debug combat on

-- 檢查寵物狀態
print("Active pets:", activePets)
print("Pet config:", PetConfig.getPet("bacterial_virus"))
```

## 結論

Bacterial Virus 寵物的成功集成展示了系統的擴展性和靈活性：

- ✅ **無縫集成**: 完全融入現有寵物系統
- ✅ **自定義支援**: 支援使用預製 3D 模型
- ✅ **戰鬥能力**: 具備完整的戰鬥功能
- ✅ **視覺效果**: 獨特的毒系主題設計
- ✅ **擴展性**: 為未來添加更多自定義寵物奠定基礎

這個實現為遊戲增加了一個強大且獨特的傳說級寵物，同時建立了支援自定義模型的框架，為未來的內容擴展提供了堅實的技術基礎。

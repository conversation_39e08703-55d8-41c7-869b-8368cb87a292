--[[
	PetUIController - 寵物UI控制器
	創建觸控友好的寵物管理界面（使用 Fusion 0.2）
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)
local Players = game:GetService("Players")
local PetConfig = require(game:GetService("ReplicatedStorage").Shared.PetConfig)

-- Fusion 0.2 API
local New = Fusion.New
local Value = Fusion.Value
local Computed = Fusion.Computed
local OnEvent = Fusion.OnEvent
local Children = Fusion.Children

local PetUIController = Knit.CreateController({
	Name = "PetUIController",
})

-- 私有變量
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
local petUI
local currentPetState = Value(nil)
local playerPetsState = Value({})
local petDexVisibleState = Value(false)
local selectedPetForDetails = Value(nil) -- 選中查看詳情的寵物

function PetUIController:KnitStart()
	print("🎨 PetUIController started")

	-- 獲取服務引用
	self.PetService = Knit.GetService("PetService")

	-- 創建寵物UI
	self:_createPetUI()

	-- 監聽服務端事件
	self:_connectToServices()

	-- 請求寵物數據
	self.PetService.GetPetDex:Fire()
end

function PetUIController:KnitInit()
	-- 初始化控制器依賴
end

-- 連接到服務端服務
function PetUIController:_connectToServices()
	-- 監聽寵物數據
	self.PetService.GetPetDex:Connect(function(pets, dexData)
		playerPetsState:set(pets)
		print("🐾 Received pet data:", pets)
	end)

	-- 監聽寵物召喚事件
	self.PetService.PetSummoned:Connect(function(petId)
		currentPetState:set(petId)
		local config = PetConfig.getPet(petId)
		if config then
			self:_showMessage("✨ " .. config.name .. " 已召喚！", Color3.fromRGB(100, 255, 100))
		end
	end)

	-- 監聽寵物收回事件
	self.PetService.PetRecalled:Connect(function()
		local previousPet = currentPetState:get()
		currentPetState:set(nil)
		if previousPet then
			local config = PetConfig.getPet(previousPet)
			if config then
				self:_showMessage("💤 " .. config.name .. " 已收回", Color3.fromRGB(255, 200, 100))
			end
		end
	end)
end

-- 創建寵物UI
function PetUIController:_createPetUI()
	petUI = New "ScreenGui" {
		Name = "PetUI",
		Parent = playerGui,
		ResetOnSpawn = false,

		[Children] = {
			-- 主要控制面板
			New "Frame" {
				Name = "PetControlPanel",
				Size = UDim2.new(0, 200, 0, 300),
				Position = UDim2.new(1, -220, 0.5, -150),
				BackgroundColor3 = Color3.fromRGB(40, 40, 40),
				BorderSizePixel = 0,

				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 15),
					},

					New "UIStroke" {
						Color = Color3.fromRGB(100, 100, 100),
						Thickness = 2,
					},

					-- 標題
					New "TextLabel" {
						Name = "Title",
						Size = UDim2.new(1, -20, 0, 40),
						Position = UDim2.new(0, 10, 0, 10),
						BackgroundTransparency = 1,
						Text = "🐾 寵物控制",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},

					-- 當前寵物顯示
					New "Frame" {
						Name = "CurrentPetFrame",
						Size = UDim2.new(1, -20, 0, 60),
						Position = UDim2.new(0, 10, 0, 60),
						BackgroundColor3 = Color3.fromRGB(60, 60, 60),
						BorderSizePixel = 0,

						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 10),
							},

							New "TextLabel" {
								Name = "CurrentPetLabel",
								Size = UDim2.new(1, -10, 1, 0),
								Position = UDim2.new(0, 5, 0, 0),
								BackgroundTransparency = 1,
								Text = Computed(function()
									local currentPet = currentPetState:get()
									if currentPet then
										local config = PetConfig.getPet(currentPet)
										if config then
											return "當前寵物: " .. config.name
										end
									end
									return "沒有召喚寵物"
								end),
								TextColor3 = Color3.fromRGB(255, 255, 255),
								TextScaled = true,
								Font = Enum.Font.Gotham,
							},
						},
					},

					-- 召喚/收回按鈕
					New "TextButton" {
						Name = "TogglePetButton",
						Size = UDim2.new(1, -20, 0, 50),
						Position = UDim2.new(0, 10, 0, 140),
						BackgroundColor3 = Computed(function()
							return currentPetState:get() and Color3.fromRGB(255, 100, 100) or Color3.fromRGB(100, 255, 100)
						end),
						BorderSizePixel = 0,
						Text = Computed(function()
							return currentPetState:get() and "收回寵物" or "召喚寵物"
						end),
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,

						[OnEvent "Activated"] = function()
							self:_togglePet()
						end,

						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 10),
							},
						},
					},

					-- 寵物圖鑑按鈕
					New "TextButton" {
						Name = "PetDexButton",
						Size = UDim2.new(1, -20, 0, 50),
						Position = UDim2.new(0, 10, 0, 210),
						BackgroundColor3 = Color3.fromRGB(100, 150, 255),
						BorderSizePixel = 0,
						Text = "📖 寵物圖鑑",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,

						[OnEvent "Activated"] = function()
							self:_togglePetDex()
						end,

						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 10),
							},
						},
					},
				},
			},

			-- 寵物圖鑑界面
			self:_createPetDexUI(),
		},
	}
end

-- 創建寵物圖鑑UI
function PetUIController:_createPetDexUI()
	return New "Frame" {
		Name = "PetDexFrame",
		Size = UDim2.new(0, 1200, 0, 700),
		Position = UDim2.new(0.5, -600, 0.5, -350),
		BackgroundColor3 = Color3.fromRGB(30, 30, 30),
		BorderSizePixel = 0,
		Visible = petDexVisibleState,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 15),
			},

			New "UIStroke" {
				Color = Color3.fromRGB(100, 150, 255),
				Thickness = 3,
			},

			-- 標題欄
			New "Frame" {
				Name = "TitleBar",
				Size = UDim2.new(1, 0, 0, 50),
				Position = UDim2.new(0, 0, 0, 0),
				BackgroundColor3 = Color3.fromRGB(100, 150, 255),
				BorderSizePixel = 0,

				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 15),
					},

					New "TextLabel" {
						Name = "Title",
						Size = UDim2.new(1, -100, 1, 0),
						Position = UDim2.new(0, 20, 0, 0),
						BackgroundTransparency = 1,
						Text = "📖 寵物圖鑑",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},

					-- 關閉按鈕
					New "TextButton" {
						Name = "CloseButton",
						Size = UDim2.new(0, 40, 0, 40),
						Position = UDim2.new(1, -50, 0, 5),
						BackgroundColor3 = Color3.fromRGB(255, 100, 100),
						BorderSizePixel = 0,
						Text = "✕",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,

						[OnEvent "Activated"] = function()
							petDexVisibleState:set(false)
						end,

						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 8),
							},
						},
					},
				},
			},

			-- 左側：寵物卡片容器
			New "ScrollingFrame" {
				Name = "PetCardsContainer",
				Size = UDim2.new(0.6, -15, 1, -70),
				Position = UDim2.new(0, 10, 0, 60),
				BackgroundTransparency = 1,
				BorderSizePixel = 0,
				ScrollBarThickness = 8,
				CanvasSize = UDim2.new(0, 0, 0, 800), -- 足夠容納4行寵物

				[Children] = {
					New "UIGridLayout" {
						CellSize = UDim2.new(0, 180, 0, 180),
						CellPadding = UDim2.new(0, 10, 0, 10),
						SortOrder = Enum.SortOrder.LayoutOrder,
						FillDirection = Enum.FillDirection.Horizontal,
						HorizontalAlignment = Enum.HorizontalAlignment.Left,
						VerticalAlignment = Enum.VerticalAlignment.Top,
						StartCorner = Enum.StartCorner.TopLeft,
					},

					-- 動態生成所有寵物卡片
					Computed(function()
						local pets = playerPetsState:get()
						local cards = {}

						-- 遍歷所有配置的寵物
						for petId, config in pairs(PetConfig.PETS) do
							local petData = pets[petId]
							local isOwned = petData ~= nil

							table.insert(cards, self:_createPetDexCard(petId, config, petData, isOwned))
						end

						return cards
					end),
				},
			},

			-- 右側：寵物詳情面板
			self:_createPetDetailsPanel(),
		},
	}
end

-- 切換寵物（召喚/收回）
function PetUIController:_togglePet()
	if currentPetState:get() then
		-- 收回當前寵物
		self.PetService.RecallPet:Fire()
	else
		-- 召喚第一隻可用的寵物
		local firstPetId = self:_getFirstAvailablePet()
		if firstPetId then
			self.PetService.SummonPet:Fire(firstPetId)
		else
			self:_showMessage("沒有可用的寵物！", Color3.fromRGB(255, 100, 100))
		end
	end
end

-- 獲取第一隻可用的寵物
function PetUIController:_getFirstAvailablePet()
	local pets = playerPetsState:get()
	for petId, petData in pairs(pets) do
		return petId -- 返回第一隻寵物
	end
	return nil
end

-- 切換寵物圖鑑顯示
function PetUIController:_togglePetDex()
	petDexVisibleState:set(not petDexVisibleState:get())
	-- 清除選中的寵物詳情
	selectedPetForDetails:set(nil)
end

-- 創建寵物詳情面板
function PetUIController:_createPetDetailsPanel()
	return New "Frame" {
		Name = "PetDetailsPanel",
		Size = UDim2.new(0.4, -15, 1, -70),
		Position = UDim2.new(0.6, 5, 0, 60),
		BackgroundColor3 = Color3.fromRGB(40, 40, 40),
		BorderSizePixel = 0,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 10),
			},

			-- 詳情內容
			Computed(function()
				local selectedPet = selectedPetForDetails:get()
				if selectedPet then
					return self:_createPetDetailsContent(selectedPet)
				else
					return self:_createEmptyDetailsContent()
				end
			end),
		},
	}
end

-- 創建空的詳情內容
function PetUIController:_createEmptyDetailsContent()
	return New "Frame" {
		Name = "EmptyContent",
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,

		[Children] = {
			New "TextLabel" {
				Size = UDim2.new(1, -40, 0, 100),
				Position = UDim2.new(0, 20, 0.5, -50),
				BackgroundTransparency = 1,
				Text = "點擊左側寵物卡片\n查看詳細資訊",
				TextColor3 = Color3.fromRGB(150, 150, 150),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Center,
				TextYAlignment = Enum.TextYAlignment.Center,
			},
		},
	}
end

-- 創建寵物詳情內容
function PetUIController:_createPetDetailsContent(petId)
	local config = PetConfig.getPet(petId)
	local playerPets = playerPetsState:get()
	local petData = playerPets[petId]
	local isOwned = petData ~= nil

	if not config then
		return self:_createEmptyDetailsContent()
	end

	return New "ScrollingFrame" {
		Name = "DetailsContent",
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
		ScrollBarThickness = 6,
		CanvasSize = UDim2.new(0, 0, 0, 800),

		[Children] = {
			New "UIListLayout" {
				SortOrder = Enum.SortOrder.LayoutOrder,
				Padding = UDim.new(0, 15),
			},

			New "UIPadding" {
				PaddingTop = UDim.new(0, 20),
				PaddingBottom = UDim.new(0, 20),
				PaddingLeft = UDim.new(0, 20),
				PaddingRight = UDim.new(0, 20),
			},

			-- 寵物頭像和基本信息
			self:_createPetHeader(petId, config, petData, isOwned),

			-- 基礎屬性
			self:_createStatsSection(config),

			-- 技能信息
			self:_createAbilitiesSection(config),

			-- 詳細備註
			isOwned and self:_createNotesSection(config) or self:_createUnknownSection(),

			-- 獲取方式
			self:_createObtainSection(config),
		},
	}
end

-- 創建寵物圖鑑卡片
function PetUIController:_createPetDexCard(petId, config, petData, isOwned)
	return New "TextButton" {
		Name = "PetDexCard_" .. petId,
		Size = UDim2.new(0, 180, 0, 180), -- 固定尺寸適合網格
		BackgroundColor3 = Computed(function()
			local selected = selectedPetForDetails:get()
			if selected == petId then
				return Color3.fromRGB(80, 120, 200) -- 選中狀態
			else
				return isOwned and Color3.fromRGB(50, 50, 50) or Color3.fromRGB(30, 30, 30)
			end
		end),
		BorderSizePixel = Computed(function()
			return selectedPetForDetails:get() == petId and 2 or 0
		end),
		BorderColor3 = Color3.fromRGB(100, 150, 255),
		Text = "",

		[OnEvent "Activated"] = function()
			selectedPetForDetails:set(petId)
			print("🔍 Selected pet for details:", petId)
		end,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 10),
			},

			New "UIStroke" {
				Color = isOwned and (PetConfig.RARITY_COLORS[config.rarity] or Color3.fromRGB(150, 150, 150)) or Color3.fromRGB(100, 100, 100),
				Thickness = 2,
			},

			-- 寵物圖標（上半部分）
			New "Frame" {
				Name = "PetIcon",
				Size = UDim2.new(1, -20, 0, 120),
				Position = UDim2.new(0, 10, 0, 10),
				BackgroundColor3 = isOwned and config.appearance.primaryColor or Color3.fromRGB(100, 100, 100),
				BorderSizePixel = 0,

				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 10),
					},

					-- 寵物表情符號
					New "TextLabel" {
						Size = UDim2.new(1, 0, 0.8, 0),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,
						Text = isOwned and self:_getPetEmoji(config.element) or "❓",
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						TextColor3 = Color3.fromRGB(255, 255, 255),
					},

					-- 稀有度標籤
					New "TextLabel" {
						Name = "RarityLabel",
						Size = UDim2.new(1, -10, 0, 20),
						Position = UDim2.new(0, 5, 1, -25),
						BackgroundColor3 = PetConfig.RARITY_COLORS[config.rarity] or Color3.fromRGB(150, 150, 150),
						BorderSizePixel = 0,
						Text = config.rarity,
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,

						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 5),
							},
						},
					},

					-- 未擁有遮罩
					isOwned and nil or New "Frame" {
						Size = UDim2.new(1, 0, 1, 0),
						BackgroundColor3 = Color3.fromRGB(0, 0, 0),
						BackgroundTransparency = 0.7,
						BorderSizePixel = 0,

						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 10),
							},
						},
					},
				},
			},

			-- 寵物信息（下半部分）
			New "Frame" {
				Name = "PetInfo",
				Size = UDim2.new(1, -20, 0, 40),
				Position = UDim2.new(0, 10, 0, 135),
				BackgroundTransparency = 1,

				[Children] = {
					-- 寵物名稱
					New "TextLabel" {
						Name = "PetName",
						Size = UDim2.new(1, 0, 0, 18),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,
						Text = isOwned and (config.name .. " Lv." .. petData.level) or config.name,
						TextColor3 = isOwned and Color3.fromRGB(255, 255, 255) or Color3.fromRGB(150, 150, 150),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						TextXAlignment = Enum.TextXAlignment.Center,
					},

					-- 屬性和狀態
					New "TextLabel" {
						Name = "PetStatus",
						Size = UDim2.new(1, 0, 0, 15),
						Position = UDim2.new(0, 0, 0, 20),
						BackgroundTransparency = 1,
						Text = isOwned and (config.element .. " | 已擁有") or (config.element .. " | 未擁有"),
						TextColor3 = isOwned and Color3.fromRGB(200, 200, 200) or Color3.fromRGB(120, 120, 120),
						TextScaled = true,
						Font = Enum.Font.Gotham,
						TextXAlignment = Enum.TextXAlignment.Center,
					},
				},
			},

			-- 召喚按鈕（覆蓋在卡片右上角）
			isOwned and New "TextButton" {
				Name = "SummonButton",
				Size = UDim2.new(0, 50, 0, 20),
				Position = UDim2.new(1, -60, 0, 10),
				BackgroundColor3 = Computed(function()
					return currentPetState:get() == petId and Color3.fromRGB(255, 100, 100) or Color3.fromRGB(100, 255, 100)
				end),
				BorderSizePixel = 0,
				Text = Computed(function()
					return currentPetState:get() == petId and "收回" or "召喚"
				end),
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,

				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 5),
					},
				},

				[OnEvent "Activated"] = function()
					if currentPetState:get() == petId then
						self.PetService.RecallPet:Fire()
					else
						self.PetService.SummonPet:Fire(petId)
						petDexVisibleState:set(false)
					end
				end,
			} or nil,
		},
	}
end

-- 獲取寵物表情符號
function PetUIController:_getPetEmoji(element)
	local emojis = {
		["火"] = "🔥",
		["水"] = "💧",
		["風"] = "🌪️",
		["土"] = "🌍",
		["雷"] = "⚡",
		["冰"] = "❄️",
		["毒"] = "☢️", -- 放射性符號代表病毒
	}
	return emojis[element] or "🐾"
end

-- 顯示消息
function PetUIController:_showMessage(text, color, duration)
	duration = duration or 3
	color = color or Color3.fromRGB(255, 255, 255)

	-- 創建臨時消息GUI
	local messageGui = Instance.new("ScreenGui")
	messageGui.Name = "PetMessage"
	messageGui.Parent = playerGui

	local messageFrame = Instance.new("Frame")
	messageFrame.Size = UDim2.new(0, 300, 0, 80)
	messageFrame.Position = UDim2.new(0.5, -150, 0.2, 0)
	messageFrame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
	messageFrame.BackgroundTransparency = 0.3
	messageFrame.BorderSizePixel = 0
	messageFrame.Parent = messageGui

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = messageFrame

	local messageLabel = Instance.new("TextLabel")
	messageLabel.Size = UDim2.new(1, -20, 1, -20)
	messageLabel.Position = UDim2.new(0, 10, 0, 10)
	messageLabel.BackgroundTransparency = 1
	messageLabel.Text = text
	messageLabel.TextColor3 = color
	messageLabel.TextScaled = true
	messageLabel.Font = Enum.Font.GothamBold
	messageLabel.TextWrapped = true
	messageLabel.Parent = messageFrame

	-- 淡入效果
	messageFrame.BackgroundTransparency = 1
	messageLabel.TextTransparency = 1

	local TweenService = game:GetService("TweenService")
	local fadeIn = TweenService:Create(
		messageFrame,
		TweenInfo.new(0.3, Enum.EasingStyle.Quad),
		{BackgroundTransparency = 0.3}
	)
	local textFadeIn = TweenService:Create(
		messageLabel,
		TweenInfo.new(0.3, Enum.EasingStyle.Quad),
		{TextTransparency = 0}
	)

	fadeIn:Play()
	textFadeIn:Play()

	-- 自動消失
	game:GetService("Debris"):AddItem(messageGui, duration)
end



-- 切換寵物（召喚/收回）
function PetUIController:_togglePet()
	if currentPetState:get() then
		-- 收回當前寵物
		self.PetService.RecallPet:Fire()
	else
		-- 召喚第一隻可用的寵物
		local firstPetId = self:_getFirstAvailablePet()
		if firstPetId then
			self.PetService.SummonPet:Fire(firstPetId)
		else
			self:_showMessage("沒有可用的寵物！", Color3.fromRGB(255, 100, 100))
		end
	end
end

-- 獲取第一隻可用的寵物
function PetUIController:_getFirstAvailablePet()
	local pets = playerPetsState:get()
	for petId, petData in pairs(pets) do
		return petId -- 返回第一隻寵物
	end
	return nil
end

-- 切換寵物圖鑑顯示
function PetUIController:_togglePetDex()
	petDexVisibleState:set(not petDexVisibleState:get())
end

-- 顯示消息
function PetUIController:_showMessage(text, color, duration)
	duration = duration or 3
	color = color or Color3.fromRGB(255, 255, 255)

	-- 創建臨時消息GUI
	local messageGui = Instance.new("ScreenGui")
	messageGui.Name = "PetMessage"
	messageGui.Parent = playerGui

	local messageFrame = Instance.new("Frame")
	messageFrame.Size = UDim2.new(0, 300, 0, 80)
	messageFrame.Position = UDim2.new(0.5, -150, 0.2, 0)
	messageFrame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
	messageFrame.BackgroundTransparency = 0.3
	messageFrame.BorderSizePixel = 0
	messageFrame.Parent = messageGui

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = messageFrame

	local messageLabel = Instance.new("TextLabel")
	messageLabel.Size = UDim2.new(1, -20, 1, -20)
	messageLabel.Position = UDim2.new(0, 10, 0, 10)
	messageLabel.BackgroundTransparency = 1
	messageLabel.Text = text
	messageLabel.TextColor3 = color
	messageLabel.TextScaled = true
	messageLabel.Font = Enum.Font.GothamBold
	messageLabel.TextWrapped = true
	messageLabel.Parent = messageFrame

	-- 淡入效果
	messageFrame.BackgroundTransparency = 1
	messageLabel.TextTransparency = 1

	local TweenService = game:GetService("TweenService")
	local fadeIn = TweenService:Create(
		messageFrame,
		TweenInfo.new(0.3, Enum.EasingStyle.Quad),
		{BackgroundTransparency = 0.3}
	)
	local textFadeIn = TweenService:Create(
		messageLabel,
		TweenInfo.new(0.3, Enum.EasingStyle.Quad),
		{TextTransparency = 0}
	)

	fadeIn:Play()
	textFadeIn:Play()

	-- 自動消失
	game:GetService("Debris"):AddItem(messageGui, duration)
end

-- 創建寵物頭像部分
function PetUIController:_createPetHeader(petId, config, petData, isOwned)
	return New "Frame" {
		Name = "PetHeader",
		Size = UDim2.new(1, 0, 0, 120),
		BackgroundColor3 = Color3.fromRGB(50, 50, 50),
		BorderSizePixel = 0,
		LayoutOrder = 1,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},

			-- 寵物圖標
			New "Frame" {
				Name = "PetIcon",
				Size = UDim2.new(0, 80, 0, 80),
				Position = UDim2.new(0, 20, 0, 20),
				BackgroundColor3 = isOwned and config.appearance.primaryColor or Color3.fromRGB(100, 100, 100),
				BorderSizePixel = 0,

				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 10),
					},

					New "TextLabel" {
						Size = UDim2.new(1, 0, 1, 0),
						BackgroundTransparency = 1,
						Text = isOwned and self:_getPetEmoji(config.element) or "❓",
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						TextColor3 = Color3.fromRGB(255, 255, 255),
					},
				},
			},

			-- 寵物信息
			New "Frame" {
				Name = "PetInfo",
				Size = UDim2.new(1, -120, 0, 80),
				Position = UDim2.new(0, 110, 0, 20),
				BackgroundTransparency = 1,

				[Children] = {
					-- 寵物名稱
					New "TextLabel" {
						Name = "PetName",
						Size = UDim2.new(1, 0, 0, 25),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,
						Text = isOwned and (config.name .. " (Lv." .. petData.level .. ")") or config.name,
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						TextXAlignment = Enum.TextXAlignment.Left,
					},

					-- 種族和元素
					New "TextLabel" {
						Name = "PetSpecies",
						Size = UDim2.new(1, 0, 0, 20),
						Position = UDim2.new(0, 0, 0, 30),
						BackgroundTransparency = 1,
						Text = config.species .. " | " .. config.element .. "屬性",
						TextColor3 = PetConfig.ELEMENT_COLORS[config.element] or Color3.fromRGB(200, 200, 200),
						TextScaled = true,
						Font = Enum.Font.Gotham,
						TextXAlignment = Enum.TextXAlignment.Left,
					},

					-- 稀有度
					New "Frame" {
						Name = "RarityFrame",
						Size = UDim2.new(0, 80, 0, 20),
						Position = UDim2.new(0, 0, 0, 55),
						BackgroundColor3 = PetConfig.RARITY_COLORS[config.rarity] or Color3.fromRGB(150, 150, 150),
						BorderSizePixel = 0,

						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 5),
							},

							New "TextLabel" {
								Size = UDim2.new(1, 0, 1, 0),
								BackgroundTransparency = 1,
								Text = config.rarity,
								TextColor3 = Color3.fromRGB(255, 255, 255),
								TextScaled = true,
								Font = Enum.Font.GothamBold,
								TextXAlignment = Enum.TextXAlignment.Center,
							},
						},
					},
				},
			},
		},
	}
end

-- 創建屬性部分
function PetUIController:_createStatsSection(config)
	return New "Frame" {
		Name = "StatsSection",
		Size = UDim2.new(1, 0, 0, 150),
		BackgroundColor3 = Color3.fromRGB(45, 45, 45),
		BorderSizePixel = 0,
		LayoutOrder = 2,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},

			-- 標題
			New "TextLabel" {
				Name = "Title",
				Size = UDim2.new(1, -20, 0, 30),
				Position = UDim2.new(0, 10, 0, 10),
				BackgroundTransparency = 1,
				Text = "📊 基礎屬性",
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextXAlignment = Enum.TextXAlignment.Left,
			},

			-- 屬性列表
			New "Frame" {
				Name = "StatsList",
				Size = UDim2.new(1, -20, 1, -50),
				Position = UDim2.new(0, 10, 0, 40),
				BackgroundTransparency = 1,

				[Children] = {
					New "UIListLayout" {
						SortOrder = Enum.SortOrder.LayoutOrder,
						Padding = UDim.new(0, 5),
					},

					-- 生命值
					self:_createStatBar("生命值", config.baseStats.health, 200, Color3.fromRGB(255, 100, 100), 1),
					-- 攻擊力
					self:_createStatBar("攻擊力", config.baseStats.attack, 100, Color3.fromRGB(255, 200, 100), 2),
					-- 防禦力
					self:_createStatBar("防禦力", config.baseStats.defense, 100, Color3.fromRGB(100, 200, 255), 3),
					-- 速度
					self:_createStatBar("速度", config.baseStats.speed, 100, Color3.fromRGB(100, 255, 100), 4),
				},
			},
		},
	}
end

-- 創建屬性條
function PetUIController:_createStatBar(statName, value, maxValue, color, layoutOrder)
	local percentage = value / maxValue

	return New "Frame" {
		Name = statName .. "Bar",
		Size = UDim2.new(1, 0, 0, 20),
		BackgroundTransparency = 1,
		LayoutOrder = layoutOrder,

		[Children] = {
			-- 屬性名稱
			New "TextLabel" {
				Name = "StatName",
				Size = UDim2.new(0, 60, 1, 0),
				Position = UDim2.new(0, 0, 0, 0),
				BackgroundTransparency = 1,
				Text = statName,
				TextColor3 = Color3.fromRGB(200, 200, 200),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Left,
			},

			-- 屬性值
			New "TextLabel" {
				Name = "StatValue",
				Size = UDim2.new(0, 40, 1, 0),
				Position = UDim2.new(1, -40, 0, 0),
				BackgroundTransparency = 1,
				Text = tostring(value),
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextXAlignment = Enum.TextXAlignment.Right,
			},

			-- 屬性條背景
			New "Frame" {
				Name = "StatBarBG",
				Size = UDim2.new(1, -110, 1, -4),
				Position = UDim2.new(0, 70, 0, 2),
				BackgroundColor3 = Color3.fromRGB(60, 60, 60),
				BorderSizePixel = 0,

				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 3),
					},

					-- 屬性條填充
					New "Frame" {
						Name = "StatBarFill",
						Size = UDim2.new(percentage, 0, 1, 0),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundColor3 = color,
						BorderSizePixel = 0,

						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 3),
							},
						},
					},
				},
			},
		},
	}
end

-- 創建技能部分
function PetUIController:_createAbilitiesSection(config)
	return New "Frame" {
		Name = "AbilitiesSection",
		Size = UDim2.new(1, 0, 0, 100),
		BackgroundColor3 = Color3.fromRGB(45, 45, 45),
		BorderSizePixel = 0,
		LayoutOrder = 3,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},

			-- 標題
			New "TextLabel" {
				Name = "Title",
				Size = UDim2.new(1, -20, 0, 30),
				Position = UDim2.new(0, 10, 0, 10),
				BackgroundTransparency = 1,
				Text = "⚡ 技能描述",
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextXAlignment = Enum.TextXAlignment.Left,
			},

			-- 描述內容
			New "TextLabel" {
				Name = "Description",
				Size = UDim2.new(1, -20, 1, -50),
				Position = UDim2.new(0, 10, 0, 40),
				BackgroundTransparency = 1,
				Text = config.description,
				TextColor3 = Color3.fromRGB(200, 200, 200),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Left,
				TextYAlignment = Enum.TextYAlignment.Top,
				TextWrapped = true,
			},
		},
	}
end

-- 創建備註部分（已擁有寵物）
function PetUIController:_createNotesSection(config)
	local notes = config.notes
	if not notes then
		return New "Frame" { Size = UDim2.new(1, 0, 0, 0), BackgroundTransparency = 1, LayoutOrder = 4 }
	end

	return New "Frame" {
		Name = "NotesSection",
		Size = UDim2.new(1, 0, 0, 300),
		BackgroundColor3 = Color3.fromRGB(45, 45, 45),
		BorderSizePixel = 0,
		LayoutOrder = 4,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},

			-- 標題
			New "TextLabel" {
				Name = "Title",
				Size = UDim2.new(1, -20, 0, 30),
				Position = UDim2.new(0, 10, 0, 10),
				BackgroundTransparency = 1,
				Text = "📝 詳細資訊",
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextXAlignment = Enum.TextXAlignment.Left,
			},

			-- 備註內容
			New "ScrollingFrame" {
				Name = "NotesContent",
				Size = UDim2.new(1, -20, 1, -50),
				Position = UDim2.new(0, 10, 0, 40),
				BackgroundTransparency = 1,
				BorderSizePixel = 0,
				ScrollBarThickness = 4,
				CanvasSize = UDim2.new(0, 0, 0, 400),

				[Children] = {
					New "UIListLayout" {
						SortOrder = Enum.SortOrder.LayoutOrder,
						Padding = UDim.new(0, 10),
					},

					-- 來源
					self:_createNoteItem("🌍 來源", notes.origin, 1),
					-- 棲息地
					self:_createNoteItem("🏠 棲息地", notes.habitat, 2),
					-- 性格
					self:_createNoteItem("😊 性格", notes.personality, 3),
					-- 特殊能力
					self:_createNoteItem("✨ 特殊能力", table.concat(notes.special_traits or {}, "、"), 4),
					-- 進化潛力
					self:_createNoteItem("🔄 進化潛力", notes.evolution_potential, 5),
				},
			},
		},
	}
end

-- 創建未知部分（未擁有寵物）
function PetUIController:_createUnknownSection()
	return New "Frame" {
		Name = "UnknownSection",
		Size = UDim2.new(1, 0, 0, 100),
		BackgroundColor3 = Color3.fromRGB(45, 45, 45),
		BorderSizePixel = 0,
		LayoutOrder = 4,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},

			New "TextLabel" {
				Size = UDim2.new(1, -20, 1, -20),
				Position = UDim2.new(0, 10, 0, 10),
				BackgroundTransparency = 1,
				Text = "❓ 詳細資訊未知\n\n需要擁有這隻寵物才能查看完整資訊",
				TextColor3 = Color3.fromRGB(150, 150, 150),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Center,
				TextYAlignment = Enum.TextYAlignment.Center,
			},
		},
	}
end

-- 創建備註項目
function PetUIController:_createNoteItem(title, content, layoutOrder)
	if not content or content == "" then
		return New "Frame" { Size = UDim2.new(1, 0, 0, 0), BackgroundTransparency = 1, LayoutOrder = layoutOrder }
	end

	return New "Frame" {
		Name = "NoteItem_" .. layoutOrder,
		Size = UDim2.new(1, 0, 0, 40),
		BackgroundTransparency = 1,
		LayoutOrder = layoutOrder,

		[Children] = {
			-- 標題
			New "TextLabel" {
				Name = "Title",
				Size = UDim2.new(1, 0, 0, 18),
				Position = UDim2.new(0, 0, 0, 0),
				BackgroundTransparency = 1,
				Text = title,
				TextColor3 = Color3.fromRGB(255, 200, 100),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextXAlignment = Enum.TextXAlignment.Left,
			},

			-- 內容
			New "TextLabel" {
				Name = "Content",
				Size = UDim2.new(1, 0, 0, 18),
				Position = UDim2.new(0, 0, 0, 20),
				BackgroundTransparency = 1,
				Text = content,
				TextColor3 = Color3.fromRGB(200, 200, 200),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Left,
				TextWrapped = true,
			},
		},
	}
end

-- 創建獲取方式部分
function PetUIController:_createObtainSection(config)
	local notes = config.notes
	if not notes then
		return New "Frame" { Size = UDim2.new(1, 0, 0, 0), BackgroundTransparency = 1, LayoutOrder = 5 }
	end

	return New "Frame" {
		Name = "ObtainSection",
		Size = UDim2.new(1, 0, 0, 120),
		BackgroundColor3 = Color3.fromRGB(45, 45, 45),
		BorderSizePixel = 0,
		LayoutOrder = 5,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},

			-- 標題
			New "TextLabel" {
				Name = "Title",
				Size = UDim2.new(1, -20, 0, 30),
				Position = UDim2.new(0, 10, 0, 10),
				BackgroundTransparency = 1,
				Text = "🎁 獲取方式",
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextXAlignment = Enum.TextXAlignment.Left,
			},

			-- 獲取方法
			New "TextLabel" {
				Name = "Method",
				Size = UDim2.new(1, -20, 0, 35),
				Position = UDim2.new(0, 10, 0, 45),
				BackgroundTransparency = 1,
				Text = notes.obtainMethod or "未知獲取方式",
				TextColor3 = Color3.fromRGB(200, 200, 200),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextXAlignment = Enum.TextXAlignment.Left,
				TextWrapped = true,
			},

			-- 稀有度機率
			New "TextLabel" {
				Name = "Chance",
				Size = UDim2.new(1, -20, 0, 25),
				Position = UDim2.new(0, 10, 0, 85),
				BackgroundTransparency = 1,
				Text = "遭遇機率: " .. (notes.rarity_chance or "未知"),
				TextColor3 = Color3.fromRGB(255, 200, 100),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextXAlignment = Enum.TextXAlignment.Left,
			},
		},
	}
end

return PetUIController

# This file is automatically @generated by <PERSON>.
# It is not intended for manual editing.
registry = "test"

[[package]]
name = "elttob/fusion"
version = "0.2.0"
dependencies = []

[[package]]
name = "evaera/promise"
version = "4.0.0"
dependencies = []

[[package]]
name = "firebird702/profileservice"
version = "1.1.0"
dependencies = []

[[package]]
name = "howmanysmall/janitor"
version = "1.18.3"
dependencies = [["Promise", "howmanysmall/typed-promise@4.0.6"]]

[[package]]
name = "howmanysmall/typed-promise"
version = "4.0.6"
dependencies = [["Promise", "evaera/promise@4.0.0"]]

[[package]]
name = "mattschrubb/zoneplus"
version = "3.2.0"
dependencies = []

[[package]]
name = "roblox/testez"
version = "0.4.1"
dependencies = []

[[package]]
name = "sleitnick/comm"
version = "1.0.1"
dependencies = [["Option", "sleitnick/option@1.0.5"], ["Promise", "evaera/promise@4.0.0"], ["Signal", "sleitnick/signal@2.0.3"]]

[[package]]
name = "sleitnick/knit"
version = "1.7.0"
dependencies = [["Comm", "sleitnick/comm@1.0.1"], ["Promise", "evaera/promise@4.0.0"]]

[[package]]
name = "sleitnick/option"
version = "1.0.5"
dependencies = []

[[package]]
name = "sleitnick/signal"
version = "1.5.0"
dependencies = []

[[package]]
name = "sleitnick/signal"
version = "2.0.3"
dependencies = []

[[package]]
name = "sleitnick/table-util"
version = "1.2.1"
dependencies = []

[[package]]
name = "your-username/pet-rpg"
version = "0.1.0"
dependencies = [["fusion", "elttob/fusion@0.2.0"], ["janitor", "howmanysmall/janitor@1.18.3"], ["knit", "sleitnick/knit@1.7.0"], ["promise", "evaera/promise@4.0.0"], ["signal", "sleitnick/signal@1.5.0"], ["tableutil", "sleitnick/table-util@1.2.1"], ["zoneplus", "mattschrubb/zoneplus@3.2.0"], ["profileservice", "firebird702/profileservice@1.1.0"], ["testez", "roblox/testez@0.4.1"]]

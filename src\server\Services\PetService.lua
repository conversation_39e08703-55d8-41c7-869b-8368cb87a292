--[[
	PetService - 寵物管理服務
	處理寵物召喚、收回、數據管理等功能
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local PetConfig = require(game:GetService("ReplicatedStorage").Shared.PetConfig)

local PetService = Knit.CreateService({
	Name = "PetService",
	Client = {
		-- 客戶端可調用的方法
		SummonPet = Knit.CreateSignal(),
		RecallPet = Knit.CreateSignal(),
		GetPetDex = Knit.CreateSignal(),
		PetSummoned = Knit.CreateSignal(),
		PetRecalled = Knit.CreateSignal(),
	},
})

-- 私有變量
local activePets = {} -- 當前召喚的寵物 {[player] = petModel}

function PetService:KnitStart()
	print("🐾 PetService started")
	
	-- 監聽客戶端請求
	self.Client.SummonPet:Connect(function(player, petId)
		self:_summonPet(player, petId)
	end)
	
	self.Client.RecallPet:Connect(function(player)
		self:_recallPet(player)
	end)
	
	self.Client.GetPetDex:Connect(function(player)
		self:_sendPetDex(player)
	end)

	-- 監聽玩家離開事件
	Players.PlayerRemoving:Connect(function(player)
		self:_cleanupPlayerPets(player)
	end)
end

function PetService:KnitInit()
	-- 獲取其他服務
	self.DataService = Knit.GetService("DataService")
end

-- 召喚寵物
function PetService:_summonPet(player, petId)
	-- 檢查玩家是否擁有這隻寵物
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData or not playerData.pets[petId] then
		warn("Player", player.Name, "doesn't own pet:", petId)
		return
	end
	
	-- 防止重複召喚同一隻寵物
	if activePets[player] then
		print("🐾 Pet already active for", player.Name, "- recalling first")
		self:_recallPet(player)
		wait(0.1) -- 短暫等待確保清理完成
	end
	
	-- 創建寵物模型
	print("🐾 Creating pet model for", petId, "for player", player.Name)
	local petModel = self:_createPetModel(petId, player)
	if petModel then
		print("🐾 Pet model created successfully:", petModel.Name, "Parent:", petModel.Parent)
		activePets[player] = petModel

		-- 更新玩家數據
		self.DataService:UpdatePlayerData(player, "activePet", petId)

		-- 通知客戶端
		self.Client.PetSummoned:Fire(player, petId)
		print("🐾 Pet summoned:", petId, "for", player.Name, "- Model in workspace:", petModel.Parent == workspace)

		-- 列出模型的所有子對象
		print("🐾 Pet model children:")
		for _, child in pairs(petModel:GetChildren()) do
			print("  -", child.Name, child.ClassName)
		end
	else
		warn("⚠️ Failed to create pet model for", petId, "for player", player.Name)
	end
end

-- 收回寵物
function PetService:_recallPet(player)
	local petModel = activePets[player]
	if petModel then
		petModel:Destroy()
		activePets[player] = nil
		
		-- 更新玩家數據
		self.DataService:UpdatePlayerData(player, "activePet", nil)
		
		-- 通知客戶端
		self.Client.PetRecalled:Fire(player)
		print("🐾 Pet recalled for", player.Name)
	end
end

-- 創建寵物模型
function PetService:_createPetModel(petId, owner)
	local petConfig = PetConfig.getPet(petId)
	if not petConfig then
		warn("Pet config not found:", petId)
		return nil
	end

	local petModel

	-- 檢查是否使用自定義模型
	if petConfig.appearance.useCustomModel and petId == "bacterial_virus" then
		-- 使用 Workspace 中的自定義模型
		print("🐾 Looking for custom model at workspace.Pet.Bacterial Virus")
		local petFolder = workspace:FindFirstChild("Pet")
		if petFolder then
			print("🐾 Pet folder found")
			local sourceModel = petFolder:FindFirstChild("Bacterial Virus")
			if sourceModel then
				print("🐾 Custom model found, cloning...")
				petModel = sourceModel:Clone()
				petModel.Name = petConfig.name
				petModel.Parent = workspace
				print("🐾 Using custom model for", petConfig.name, "- Model parent:", petModel.Parent)
			else
				warn("Custom model 'Bacterial Virus' not found in workspace.Pet - falling back to generated model")
				print("🐾 Available models in Pet folder:")
				for _, child in pairs(petFolder:GetChildren()) do
					print("  -", child.Name, child.ClassName)
				end
				petModel = self:_createGeneratedPetModel(petConfig)
			end
		else
			warn("Pet folder not found in workspace - falling back to generated model")
			petModel = self:_createGeneratedPetModel(petConfig)
		end
	else
		-- 創建生成的寵物模型
		print("🐾 Creating generated model for", petConfig.name)
		petModel = self:_createGeneratedPetModel(petConfig)
	end

	-- 確保模型有必要的組件
	if petModel then
		self:_setupPetModel(petModel, petConfig, owner)
		print("🐾 Pet model creation completed for", petConfig.name)
	else
		warn("⚠️ Pet model is nil after creation for", petConfig.name)
	end

	return petModel
end

-- 創建生成的寵物模型
function PetService:_createGeneratedPetModel(petConfig)
	local petModel = Instance.new("Model")
	petModel.Name = petConfig.name
	petModel.Parent = workspace

	-- 創建主體部分
	local body = Instance.new("Part")
	body.Name = "Body"
	body.Size = petConfig.appearance.size
	body.Color = petConfig.appearance.primaryColor
	body.Material = Enum.Material.Neon
	body.Shape = Enum.PartType.Block
	body.TopSurface = Enum.SurfaceType.Smooth
	body.BottomSurface = Enum.SurfaceType.Smooth
	body.CanCollide = false
	body.Parent = petModel
	
	-- 添加圓角
	local corner = Instance.new("SpecialMesh")
	corner.MeshType = Enum.MeshType.Sphere
	corner.Parent = body
	
	-- 創建眼睛
	local leftEye = Instance.new("Part")
	leftEye.Name = "LeftEye"
	leftEye.Size = Vector3.new(0.3, 0.3, 0.3)
	leftEye.Color = Color3.fromRGB(0, 0, 0)
	leftEye.Material = Enum.Material.Neon
	leftEye.Shape = Enum.PartType.Ball
	leftEye.CanCollide = false
	leftEye.Parent = petModel
	
	local rightEye = leftEye:Clone()
	rightEye.Name = "RightEye"
	rightEye.Parent = petModel
	
	-- 焊接眼睛到身體
	local leftWeld = Instance.new("WeldConstraint")
	leftWeld.Part0 = body
	leftWeld.Part1 = leftEye
	leftWeld.Parent = body
	leftEye.CFrame = body.CFrame * CFrame.new(-0.4, 0.3, -petConfig.appearance.size.Z/2 + 0.1)
	
	local rightWeld = Instance.new("WeldConstraint")
	rightWeld.Part0 = body
	rightWeld.Part1 = rightEye
	rightWeld.Parent = body
	rightEye.CFrame = body.CFrame * CFrame.new(0.4, 0.3, -petConfig.appearance.size.Z/2 + 0.1)
	
	-- 添加發光效果
	if petConfig.appearance.glowEffect then
		local pointLight = Instance.new("PointLight")
		pointLight.Color = petConfig.appearance.primaryColor
		pointLight.Brightness = 0.5
		pointLight.Range = 10
		pointLight.Parent = body
	end
	
	-- 創建 Humanoid 用於移動
	local humanoid = Instance.new("Humanoid")
	humanoid.MaxHealth = 100
	humanoid.Health = 100
	humanoid.WalkSpeed = petConfig.followSettings.speed
	humanoid.JumpPower = petConfig.followSettings.jumpPower
	humanoid.Parent = petModel
	
	-- 創建 HumanoidRootPart
	local rootPart = Instance.new("Part")
	rootPart.Name = "HumanoidRootPart"
	rootPart.Size = Vector3.new(2, 2, 1) -- 增大尺寸確保穩定
	rootPart.Transparency = 1
	rootPart.CanCollide = false -- 保持 false 避免卡住
	rootPart.Anchored = false -- 確保不被錨定
	rootPart.Parent = petModel
	
	-- 焊接身體到根部件
	local bodyWeld = Instance.new("WeldConstraint")
	bodyWeld.Part0 = rootPart
	bodyWeld.Part1 = body
	bodyWeld.Parent = rootPart
	
	-- 設置主要部件
	petModel.PrimaryPart = rootPart

	-- 確保 PrimaryPart 設置成功
	if not petModel.PrimaryPart then
		warn("⚠️ Failed to set PrimaryPart for generated pet model:", petConfig.name)
	end
	
	-- 生成模型完成，返回給 _setupPetModel 處理其他邏輯

	return petModel
end

-- 設置寵物模型的通用組件
function PetService:_setupPetModel(petModel, petConfig, owner)
	print("🐾 Setting up pet model:", petConfig.name, "for", owner.Name)
	print("🐾 Model children before setup:")
	for _, child in pairs(petModel:GetChildren()) do
		print("  -", child.Name, child.ClassName)
	end

	-- 確保模型有 Humanoid
	local humanoid = petModel:FindFirstChild("Humanoid")
	if not humanoid then
		print("🐾 Creating Humanoid for", petConfig.name)
		humanoid = Instance.new("Humanoid")
		humanoid.Parent = petModel
	else
		print("🐾 Humanoid already exists for", petConfig.name)
	end
	humanoid.MaxHealth = 100
	humanoid.Health = 100
	humanoid.WalkSpeed = petConfig.followSettings.speed
	humanoid.JumpPower = petConfig.followSettings.jumpPower

	-- 確保模型有 HumanoidRootPart
	local rootPart = petModel:FindFirstChild("HumanoidRootPart")
	if not rootPart then
		print("🐾 Creating HumanoidRootPart for", petConfig.name)
		rootPart = Instance.new("Part")
		rootPart.Name = "HumanoidRootPart"
		rootPart.Size = Vector3.new(2, 2, 1)
		rootPart.Transparency = 1
		rootPart.CanCollide = false
		rootPart.Anchored = false
		rootPart.Parent = petModel

		-- 如果有主體部分，焊接到根部件
		local body = petModel:FindFirstChild("Body") or petModel:FindFirstChildOfClass("Part")
		if body and body ~= rootPart then
			print("🐾 Welding body to HumanoidRootPart for", petConfig.name, "- Body:", body.Name)
			local bodyWeld = Instance.new("WeldConstraint")
			bodyWeld.Part0 = rootPart
			bodyWeld.Part1 = body
			bodyWeld.Parent = rootPart
		else
			print("🐾 No body part found to weld for", petConfig.name)
		end
	else
		print("🐾 HumanoidRootPart already exists for", petConfig.name, "- Size:", rootPart.Size)
		-- 確保現有的 HumanoidRootPart 有正確的屬性
		rootPart.CanCollide = false
		rootPart.Anchored = false
		if rootPart.Transparency > 0.5 then
			rootPart.Transparency = 1 -- 確保透明
		end
	end

	-- 設置主要部件 - 使用多種方法確保成功
	print("🐾 Setting PrimaryPart for", petConfig.name, "to", rootPart.Name, "- RootPart parent:", rootPart.Parent and rootPart.Parent.Name or "nil")

	-- 方法1: 直接設置
	petModel.PrimaryPart = rootPart

	-- 方法2: 如果失敗，等待一幀後重試
	if not petModel.PrimaryPart then
		print("🐾 First attempt failed, retrying...")
		task.wait()
		petModel.PrimaryPart = rootPart
	end

	-- 方法3: 如果還是失敗，使用 SetPrimaryPartCFrame 方法
	if not petModel.PrimaryPart then
		print("🐾 Second attempt failed, trying alternative method...")
		-- 確保 rootPart 在模型中
		if rootPart.Parent ~= petModel then
			rootPart.Parent = petModel
		end
		task.wait()
		petModel.PrimaryPart = rootPart
	end

	-- 最終驗證
	if petModel.PrimaryPart then
		print("🐾 PrimaryPart set successfully for", petConfig.name, "to", petModel.PrimaryPart.Name)
	else
		warn("⚠️ Failed to set PrimaryPart for pet model:", petConfig.name, "after all attempts")
		-- 列出所有 Part 類型的子對象
		print("🐾 Available Parts in model:")
		for _, child in pairs(petModel:GetChildren()) do
			if child:IsA("BasePart") then
				print("  -", child.Name, child.ClassName, "Size:", child.Size)
			end
		end
	end

	-- 添加寵物標籤
	local ownerValue = petModel:FindFirstChild("Owner")
	if not ownerValue then
		ownerValue = Instance.new("ObjectValue")
		ownerValue.Name = "Owner"
		ownerValue.Parent = petModel
	end
	ownerValue.Value = owner

	local petIdValue = petModel:FindFirstChild("PetId")
	if not petIdValue then
		petIdValue = Instance.new("StringValue")
		petIdValue.Name = "PetId"
		petIdValue.Parent = petModel
	end
	petIdValue.Value = petConfig.id

	-- 設置初始位置（在玩家旁邊）
	if owner.Character and owner.Character:FindFirstChild("HumanoidRootPart") then
		local playerPos = owner.Character.HumanoidRootPart.Position
		local initialPos = playerPos + Vector3.new(3, 2, 0) -- 稍微抬高避免卡在地面

		if petModel.PrimaryPart then
			petModel:PivotTo(CFrame.new(initialPos))
			print("🐾 Pet positioned at:", initialPos, "Player at:", playerPos)
		else
			warn("⚠️ Cannot position pet - no PrimaryPart set")
		end
	else
		print("⚠️ Could not position pet - player character not found")
	end

	-- 添加調試信息
	print("🐾 Pet model setup completed:", petConfig.name, "for", owner.Name)
end

-- 發送寵物圖鑑數據
function PetService:_sendPetDex(player)
	local playerData = self.DataService:GetPlayerData(player)
	if playerData then
		self.Client.GetPetDex:Fire(player, playerData.pets, playerData.petDex)
	end
end

-- 玩家離開時清理寵物
function PetService:_cleanupPlayerPets(player)
	if activePets[player] then
		activePets[player]:Destroy()
		activePets[player] = nil
	end
end

return PetService

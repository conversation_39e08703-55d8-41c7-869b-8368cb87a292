--[[
	PetConfig - 寵物配置數據
	定義所有寵物的基本屬性和外觀
]]

local PetConfig = {}

-- 寵物圖鑑數據
PetConfig.PETS = {
	["fire_fox"] = {
		id = "fire_fox",
		name = "火焰狐狸",
		species = "狐狸",
		element = "火",
		rarity = "普通",
		description = "生活在火山附近的靈巧狐狸，擅長火焰攻擊。",

		-- 詳細備註信息
		notes = {
			origin = "火山地帶",
			habitat = "炎熱的火山口附近，以火焰能量為食",
			personality = "活潑好動，忠誠度高，喜歡溫暖的環境",
			obtainMethod = "在火山區域探索時有機會遇到",
			rarity_chance = "15%",
			special_traits = {"火焰免疫", "夜視能力"},
			evolution_potential = "可進化為烈焰狐王",
		},
		
		-- 基礎屬性
		baseStats = {
			health = 80,
			attack = 95,
			defense = 60,
			speed = 85,
		},
		
		-- 外觀配置
		appearance = {
			primaryColor = Color3.fromRGB(255, 100, 50),  -- 橙紅色
			secondaryColor = Color3.fromRGB(255, 200, 100), -- 淡黃色
			size = Vector3.new(2, 1.5, 3), -- 長寬高
			glowEffect = true,
		},
		
		-- 技能
		abilities = {"火球術", "火焰衝刺"},
		
		-- 跟隨設置
		followSettings = {
			distance = 5, -- 跟隨距離
			speed = 16,   -- 移動速度
			jumpPower = 50,
		},
	},
	
	["crystal_turtle"] = {
		id = "crystal_turtle",
		name = "水晶龜",
		species = "烏龜",
		element = "水",
		rarity = "稀有",
		description = "背殼由堅硬水晶構成的神秘烏龜，防禦力極強。",

		-- 詳細備註信息
		notes = {
			origin = "深海水晶洞穴",
			habitat = "富含礦物質的深海區域，以水晶為食",
			personality = "沉穩冷靜，極具耐心，是優秀的守護者",
			obtainMethod = "深海探險或水晶洞穴挖掘",
			rarity_chance = "8%",
			special_traits = {"水下呼吸", "水晶硬化", "自我修復"},
			evolution_potential = "可進化為鑽石巨龜",
		},
		
		-- 基礎屬性
		baseStats = {
			health = 120,
			attack = 65,
			defense = 110,
			speed = 45,
		},
		
		-- 外觀配置
		appearance = {
			primaryColor = Color3.fromRGB(100, 200, 255),  -- 水藍色
			secondaryColor = Color3.fromRGB(200, 255, 255), -- 淡藍色
			size = Vector3.new(2.5, 2, 2.5),
			glowEffect = true,
		},
		
		-- 技能
		abilities = {"水盾", "治癒波動"},
		
		-- 跟隨設置
		followSettings = {
			distance = 4,
			speed = 12,
			jumpPower = 30,
		},
	},
	
	["wind_bird"] = {
		id = "wind_bird",
		name = "風之鳥",
		species = "鳥類",
		element = "風",
		rarity = "史詩",
		description = "掌控風之力量的神秘鳥類，速度如閃電般迅捷。",

		-- 詳細備註信息
		notes = {
			origin = "天空之城遺跡",
			habitat = "高空雲層中，以風之精華為食",
			personality = "自由奔放，難以馴服，但一旦認主便終生忠誠",
			obtainMethod = "完成天空神殿試煉或極稀有野外遭遇",
			rarity_chance = "3%",
			special_traits = {"飛行能力", "風暴召喚", "瞬間移動"},
			evolution_potential = "已是最終形態",
		},
		
		-- 基礎屬性
		baseStats = {
			health = 70,
			attack = 85,
			defense = 55,
			speed = 120,
		},
		
		-- 外觀配置
		appearance = {
			primaryColor = Color3.fromRGB(150, 255, 150),  -- 淡綠色
			secondaryColor = Color3.fromRGB(255, 255, 255), -- 白色
			size = Vector3.new(1.5, 1.5, 2),
			glowEffect = true,
		},
		
		-- 技能
		abilities = {"風刃", "疾風步"},
		
		-- 跟隨設置
		followSettings = {
			distance = 6,
			speed = 20,
			jumpPower = 60,
			canFly = true, -- 特殊：可以飛行
		},
	},

	["bacterial_virus"] = {
		id = "bacterial_virus",
		name = "細菌病毒",
		species = "病毒",
		element = "毒",
		rarity = "傳說",
		description = "來自深淵的神秘病毒生物，擁有強大的感染和腐蝕能力。",

		-- 詳細備註信息
		notes = {
			origin = "深淵實驗室",
			habitat = "污染的實驗環境中，以負能量為食",
			personality = "神秘莫測，具有強烈的攻擊性，但對主人絕對忠誠",
			obtainMethod = "完成深淵實驗室的危險任務或極稀有突變",
			rarity_chance = "1%",
			special_traits = {"毒素免疫", "感染攻擊", "自我複製", "腐蝕防護"},
			evolution_potential = "已是最終變異形態",
		},

		-- 基礎屬性
		baseStats = {
			health = 150,
			attack = 120,
			defense = 80,
			speed = 100,
		},

		-- 外觀配置
		appearance = {
			primaryColor = Color3.fromRGB(100, 255, 100),  -- 毒綠色
			secondaryColor = Color3.fromRGB(50, 200, 50), -- 深綠色
			size = Vector3.new(2, 2, 2),
			glowEffect = true,
			useCustomModel = true, -- 標記使用自定義模型
		},

		-- 技能
		abilities = {"毒液噴射", "病毒感染", "腐蝕光環"},

		-- 跟隨設置
		followSettings = {
			distance = 4,
			speed = 18,
			jumpPower = 45,
			canFloat = true, -- 特殊：可以漂浮
		},
	},
}

-- 稀有度配置
PetConfig.RARITY_COLORS = {
	["普通"] = Color3.fromRGB(150, 150, 150),
	["稀有"] = Color3.fromRGB(100, 150, 255),
	["史詩"] = Color3.fromRGB(150, 100, 255),
	["傳說"] = Color3.fromRGB(255, 200, 50),
}

-- 元素配置
PetConfig.ELEMENT_COLORS = {
	["火"] = Color3.fromRGB(255, 100, 50),
	["水"] = Color3.fromRGB(100, 150, 255),
	["風"] = Color3.fromRGB(150, 255, 150),
	["土"] = Color3.fromRGB(150, 100, 50),
	["毒"] = Color3.fromRGB(100, 255, 100),
}

-- 獲取寵物配置
function PetConfig.getPet(petId)
	return PetConfig.PETS[petId]
end

-- 獲取所有寵物ID
function PetConfig.getAllPetIds()
	local ids = {}
	for id, _ in pairs(PetConfig.PETS) do
		table.insert(ids, id)
	end
	return ids
end

-- 根據稀有度獲取寵物
function PetConfig.getPetsByRarity(rarity)
	local pets = {}
	for id, pet in pairs(PetConfig.PETS) do
		if pet.rarity == rarity then
			pets[id] = pet
		end
	end
	return pets
end

return PetConfig

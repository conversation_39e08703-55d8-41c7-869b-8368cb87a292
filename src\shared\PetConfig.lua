--[[
	PetConfig - 寵物配置數據
	定義所有寵物的基本屬性和外觀
]]

local PetConfig = {}

-- 寵物圖鑑數據
PetConfig.PETS = {
	["fire_fox"] = {
		id = "fire_fox",
		name = "火焰狐狸",
		species = "狐狸",
		element = "火",
		rarity = "普通",
		description = "生活在火山附近的靈巧狐狸，擅長火焰攻擊。",
		
		-- 基礎屬性
		baseStats = {
			health = 80,
			attack = 95,
			defense = 60,
			speed = 85,
		},
		
		-- 外觀配置
		appearance = {
			primaryColor = Color3.fromRGB(255, 100, 50),  -- 橙紅色
			secondaryColor = Color3.fromRGB(255, 200, 100), -- 淡黃色
			size = Vector3.new(2, 1.5, 3), -- 長寬高
			glowEffect = true,
		},
		
		-- 技能
		abilities = {"火球術", "火焰衝刺"},
		
		-- 跟隨設置
		followSettings = {
			distance = 5, -- 跟隨距離
			speed = 16,   -- 移動速度
			jumpPower = 50,
		},
	},
	
	["crystal_turtle"] = {
		id = "crystal_turtle",
		name = "水晶龜",
		species = "烏龜",
		element = "水",
		rarity = "稀有",
		description = "背殼由堅硬水晶構成的神秘烏龜，防禦力極強。",
		
		-- 基礎屬性
		baseStats = {
			health = 120,
			attack = 65,
			defense = 110,
			speed = 45,
		},
		
		-- 外觀配置
		appearance = {
			primaryColor = Color3.fromRGB(100, 200, 255),  -- 水藍色
			secondaryColor = Color3.fromRGB(200, 255, 255), -- 淡藍色
			size = Vector3.new(2.5, 2, 2.5),
			glowEffect = true,
		},
		
		-- 技能
		abilities = {"水盾", "治癒波動"},
		
		-- 跟隨設置
		followSettings = {
			distance = 4,
			speed = 12,
			jumpPower = 30,
		},
	},
	
	["wind_bird"] = {
		id = "wind_bird",
		name = "風之鳥",
		species = "鳥類",
		element = "風",
		rarity = "史詩",
		description = "掌控風之力量的神秘鳥類，速度如閃電般迅捷。",
		
		-- 基礎屬性
		baseStats = {
			health = 70,
			attack = 85,
			defense = 55,
			speed = 120,
		},
		
		-- 外觀配置
		appearance = {
			primaryColor = Color3.fromRGB(150, 255, 150),  -- 淡綠色
			secondaryColor = Color3.fromRGB(255, 255, 255), -- 白色
			size = Vector3.new(1.5, 1.5, 2),
			glowEffect = true,
		},
		
		-- 技能
		abilities = {"風刃", "疾風步"},
		
		-- 跟隨設置
		followSettings = {
			distance = 6,
			speed = 20,
			jumpPower = 60,
			canFly = true, -- 特殊：可以飛行
		},
	},
}

-- 稀有度配置
PetConfig.RARITY_COLORS = {
	["普通"] = Color3.fromRGB(150, 150, 150),
	["稀有"] = Color3.fromRGB(100, 150, 255),
	["史詩"] = Color3.fromRGB(150, 100, 255),
	["傳說"] = Color3.fromRGB(255, 200, 50),
}

-- 元素配置
PetConfig.ELEMENT_COLORS = {
	["火"] = Color3.fromRGB(255, 100, 50),
	["水"] = Color3.fromRGB(100, 150, 255),
	["風"] = Color3.fromRGB(150, 255, 150),
	["土"] = Color3.fromRGB(150, 100, 50),
}

-- 獲取寵物配置
function PetConfig.getPet(petId)
	return PetConfig.PETS[petId]
end

-- 獲取所有寵物ID
function PetConfig.getAllPetIds()
	local ids = {}
	for id, _ in pairs(PetConfig.PETS) do
		table.insert(ids, id)
	end
	return ids
end

-- 根據稀有度獲取寵物
function PetConfig.getPetsByRarity(rarity)
	local pets = {}
	for id, pet in pairs(PetConfig.PETS) do
		if pet.rarity == rarity then
			pets[id] = pet
		end
	end
	return pets
end

return PetConfig

--[[
	Pet RPG - Client Initialization
	使用 Knit 框架初始化客戶端
]]

-- 載入依賴
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

-- 載入所有控制器
local Controllers = script.Controllers
for _, controllerModule in pairs(Controllers:GetChildren()) do
	if controllerModule:IsA("ModuleScript") then
		require(controllerModule)
	end
end

-- 啟動 Knit 客戶端
Knit.Start():andThen(function()
	print("🎮 Pet RPG Client Started!")
end):catch(function(err)
	warn("❌ Client startup failed:", err)
end)

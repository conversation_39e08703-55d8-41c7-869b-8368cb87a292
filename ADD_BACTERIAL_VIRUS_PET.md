# 添加 Bacterial Virus 寵物指南

## 概述
已成功將 Workspace.Pet.Bacterial Virus 模型集成到寵物系統和怪物系統中。

## 新增內容

### 🐾 寵物配置
- **ID**: `bacterial_virus`
- **名稱**: 細菌病毒
- **種族**: 病毒體
- **元素**: 毒 (新增元素)
- **稀有度**: 史詩
- **表情符號**: ☢️ (放射性符號)

### 👹 怪物配置
- **等級**: 5
- **生命值**: 150
- **攻擊力**: 35
- **防禦力**: 25
- **速度**: 20
- **掉落機率**: 2% 機率掉落寵物

## 技術實現

### 1. 寵物系統集成
```lua
-- PetConfig.lua 中的配置
["bacterial_virus"] = {
    id = "bacterial_virus",
    name = "細菌病毒",
    species = "病毒體",
    element = "毒",
    rarity = "史詩",
    appearance = {
        modelName = "Bacterial Virus" -- 使用 Workspace 中的模型
    }
}
```

### 2. 怪物系統集成
```lua
-- MonsterConfig.lua 中的配置
bacterial_virus = {
    name = "細菌病毒",
    level = 5,
    appearance = {
        modelName = "Bacterial Virus" -- 使用相同模型
    }
}
```

### 3. 模型加載邏輯
- **PetService**: 檢查 `workspace.Pet.Bacterial Virus` 並複製模型
- **MonsterService**: 同樣支援預製模型加載
- **自動回退**: 如果找不到預製模型，使用程序化生成

## 給玩家添加寵物的方法

### 方法1: 使用服務器控制台
```lua
-- 在服務器控制台執行
local Players = game:GetService("Players")
local DataService = require(game.ServerScriptService.Services.DataService)

local player = Players:FindFirstChild("玩家名稱") -- 替換為實際玩家名稱
if player then
    local playerData = DataService:GetPlayerData(player)
    if playerData then
        -- 添加寵物到玩家數據
        playerData.pets["bacterial_virus"] = {
            level = 1,
            experience = 0,
            obtainedAt = os.time()
        }
        
        -- 保存數據
        DataService:SavePlayerData(player)
        print("✅ 已為玩家", player.Name, "添加 Bacterial Virus 寵物")
    end
end
```

### 方法2: 修改 DataService 默認數據
```lua
-- 在 DataService.lua 中修改默認寵物
local defaultData = {
    pets = {
        ["fire_fox"] = { level = 1, experience = 0 },
        ["bacterial_virus"] = { level = 1, experience = 0 } -- 添加這行
    }
}
```

### 方法3: 創建管理員命令
```lua
-- 在 ChatService 或命令系統中添加
if message == "/give_virus" and player.Name == "管理員名稱" then
    local DataService = require(game.ServerScriptService.Services.DataService)
    local playerData = DataService:GetPlayerData(player)
    playerData.pets["bacterial_virus"] = { level = 1, experience = 0 }
    DataService:SavePlayerData(player)
end
```

## 測試步驟

### 1. 驗證模型存在
```lua
-- 檢查模型是否在正確位置
local model = workspace.Pet:FindFirstChild("Bacterial Virus")
if model then
    print("✅ 模型找到:", model.Name)
else
    print("❌ 模型未找到，請檢查 Workspace.Pet.Bacterial Virus")
end
```

### 2. 測試寵物召喚
1. 給玩家添加寵物（使用上述方法之一）
2. 打開寵物圖鑑
3. 查看 "細菌病毒" 是否出現
4. 點擊查看詳細資訊
5. 嘗試召喚寵物

### 3. 測試怪物生成
```lua
-- 在服務器控制台生成怪物
local MonsterService = require(game.ServerScriptService.Services.MonsterService)
local player = game.Players:FindFirstChild("玩家名稱")
if player and player.Character then
    local position = player.Character.HumanoidRootPart.Position + Vector3.new(10, 0, 0)
    MonsterService:SpawnMonster(player, "bacterial_virus", position)
end
```

## 特殊功能

### 毒元素支援
- 新增毒元素顏色：紫色 `Color3.fromRGB(150, 50, 200)`
- 表情符號：☢️ 放射性符號
- 在寵物圖鑑中正確顯示

### 詳細備註信息
- **來源**: 深淵實驗室
- **棲息地**: 污染的實驗室和下水道系統
- **性格**: 狡猾陰險，具有強烈的攻擊性
- **特殊能力**: 毒素免疫、感染攻擊、自我複製、腐蝕防護
- **獲取方式**: 完成深淵實驗室探索任務或擊敗病毒之王 (2%機率)

### 戰鬥能力
- **毒液噴射**: 25傷害，3秒冷卻，15格範圍
- **病毒感染**: 15傷害，8秒冷卻，10格範圍，附加中毒效果

## 故障排除

### 常見問題

1. **模型不顯示**
   - 檢查 `workspace.Pet.Bacterial Virus` 是否存在
   - 確認模型有正確的 Part 結構

2. **寵物無法召喚**
   - 確認玩家數據中有該寵物
   - 檢查控制台是否有錯誤信息

3. **怪物不生成**
   - 檢查 MonsterConfig 是否正確加載
   - 確認模型複製邏輯正常工作

### 調試命令
```lua
-- 檢查玩家寵物數據
local player = game.Players.LocalPlayer
local DataService = require(game.ServerScriptService.Services.DataService)
local data = DataService:GetPlayerData(player)
print("玩家寵物:", data.pets)

-- 檢查寵物配置
local PetConfig = require(game.ReplicatedStorage.Shared.PetConfig)
local config = PetConfig.getPet("bacterial_virus")
print("寵物配置:", config)

-- 檢查怪物配置
local MonsterConfig = require(game.ReplicatedStorage.Shared.MonsterConfig)
local monsterConfig = MonsterConfig.getMonster("bacterial_virus")
print("怪物配置:", monsterConfig)
```

## 後續擴展

### 短期改進
1. 添加病毒感染視覺效果
2. 實現毒素傷害的持續效果
3. 添加病毒複製技能動畫

### 長期規劃
1. 病毒進化系統
2. 感染傳播機制
3. 實驗室副本系統
4. 更多病毒類寵物

## 結論

Bacterial Virus 已成功集成到遊戲系統中，支援：
- ✅ 寵物召喚和戰鬥
- ✅ 怪物生成和掉落
- ✅ 圖鑑展示和詳細信息
- ✅ 使用自定義 3D 模型
- ✅ 完整的配置和平衡性

玩家現在可以通過擊敗病毒怪物或管理員給予的方式獲得這隻獨特的史詩級寵物！

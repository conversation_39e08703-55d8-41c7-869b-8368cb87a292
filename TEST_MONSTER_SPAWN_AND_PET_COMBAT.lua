--[[
    測試怪物生成和寵物戰鬥修復
    在服務器控制台執行這個腳本
]]

local Players = game:GetService("Players")

print("=== 測試怪物生成和寵物戰鬥修復 ===")

-- 獲取第一個玩家
local player = Players:GetPlayers()[1]
if not player then
    print("❌ 沒有玩家在線")
    return
end

print("🧪 測試玩家:", player.Name)

-- 1. 檢查 Workspace 中的模型
local function checkWorkspaceModels()
    print("\n1. 檢查 Workspace 中的模型...")
    
    local petFolder = workspace:FindFirstChild("Pet")
    if petFolder then
        local bacterialModel = petFolder:FindFirstChild("Bacterial Virus")
        if bacterialModel then
            print("✅ 源模型存在:", bacterialModel.Name)
            print("   - 類型:", bacterialModel.ClassName)
            print("   - 子物件數量:", #bacterialModel:GetChildren())
            
            -- 檢查關鍵組件
            local humanoid = bacterialModel:FindFirstChildOfClass("Humanoid")
            local rootPart = bacterialModel:FindFirstChild("HumanoidRootPart") or bacterialModel.PrimaryPart
            
            print("   - Humanoid:", humanoid and "存在" or "缺失")
            print("   - RootPart:", rootPart and "存在" or "缺失")
            
            return true
        else
            print("❌ 源模型不存在: Bacterial Virus")
            return false
        end
    else
        print("❌ Pet 文件夾不存在")
        return false
    end
end

-- 2. 測試怪物生成
local function testMonsterSpawn()
    print("\n2. 測試怪物生成...")
    
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 玩家角色不存在")
        return false
    end
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    local position = player.Character.HumanoidRootPart.Position + Vector3.new(15, 0, 0)
    
    print("🎯 生成位置:", position)
    
    -- 清理現有怪物
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "bacterial_virus_") then
            print("🧹 清理舊怪物:", model.Name)
            model:Destroy()
        end
    end
    
    wait(0.5)
    
    -- 生成新怪物
    MonsterService:SpawnMonster(player, "bacterial_virus", position)
    wait(1)
    
    -- 檢查是否成功生成
    local monsterFound = false
    local monsterName = ""
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "bacterial_virus_") then
            monsterFound = true
            monsterName = model.Name
            print("✅ 怪物生成成功:", model.Name)
            
            -- 檢查怪物結構
            local humanoid = model:FindFirstChildOfClass("Humanoid")
            local rootPart = model:FindFirstChild("HumanoidRootPart")
            
            print("   - Humanoid:", humanoid and ("存在, 血量: " .. humanoid.Health .. "/" .. humanoid.MaxHealth) or "缺失")
            print("   - RootPart:", rootPart and "存在" or "缺失")
            
            if rootPart then
                print("   - 位置:", rootPart.Position)
            end
            
            break
        end
    end
    
    if not monsterFound then
        print("❌ 怪物生成失敗")
        print("Workspace 中的模型:")
        for _, model in pairs(workspace:GetChildren()) do
            if model:IsA("Model") and string.find(model.Name, "_") then
                print("   -", model.Name)
            end
        end
    end
    
    return monsterFound, monsterName
end

-- 3. 測試寵物召喚
local function testPetSummon()
    print("\n3. 測試寵物召喚...")
    
    local PetService = require(game.ServerScriptService.Services.PetService)
    
    -- 收回現有寵物
    PetService:_recallPet(player)
    wait(0.5)
    
    -- 召喚 bacterial_virus
    PetService:_summonPet(player, "bacterial_virus")
    wait(1)
    
    -- 檢查是否成功召喚
    local petFound = false
    local petName = ""
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and (string.find(model.Name, "細菌病毒") or string.find(model.Name, "Bacterial")) then
            petFound = true
            petName = model.Name
            print("✅ 寵物召喚成功:", model.Name)
            
            -- 檢查寵物結構
            local humanoid = model:FindFirstChildOfClass("Humanoid")
            local rootPart = model:FindFirstChild("HumanoidRootPart")
            local owner = model:FindFirstChild("Owner")
            local petId = model:FindFirstChild("PetId")
            
            print("   - Humanoid:", humanoid and "存在" or "缺失")
            print("   - RootPart:", rootPart and "存在" or "缺失")
            print("   - Owner:", owner and owner.Value.Name or "缺失")
            print("   - PetId:", petId and petId.Value or "缺失")
            
            break
        end
    end
    
    if not petFound then
        print("❌ 寵物召喚失敗")
    end
    
    return petFound, petName
end

-- 4. 測試寵物戰鬥AI
local function testPetCombatAI(petName, monsterName)
    print("\n4. 測試寵物戰鬥AI...")
    
    if not petName or not monsterName then
        print("❌ 寵物或怪物不存在，無法測試戰鬥")
        return false
    end
    
    print("🔍 監控寵物戰鬥行為...")
    print("   寵物:", petName)
    print("   怪物:", monsterName)
    
    -- 等待寵物AI檢測
    print("⏳ 等待10秒觀察寵物AI...")
    for i = 1, 10 do
        wait(1)
        print("   等待中... " .. i .. "/10")
        
        -- 檢查怪物是否還存在
        local monster = workspace:FindFirstChild(monsterName)
        if not monster then
            print("💀 怪物已被擊殺!")
            return true
        end
        
        -- 檢查怪物血量
        local humanoid = monster:FindFirstChildOfClass("Humanoid")
        if humanoid then
            print("   怪物血量:", humanoid.Health .. "/" .. humanoid.MaxHealth)
        end
    end
    
    print("💡 請觀察控制台是否有以下戰鬥信息:")
    print("   - '🐾 Pet AI: Found X monsters but none in range'")
    print("   - '🐾 Pet found monster: ...'")
    print("   - '🐾 Pet [petId] attacking monster [instanceId]'")
    print("   - '☢️ Pet virus attack!'")
    
    return false
end

-- 5. 檢查活躍怪物列表
local function checkActiveMonsters()
    print("\n5. 檢查活躍怪物列表...")
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    local activeMonsters = MonsterService:GetActiveMonsters()
    
    print("活躍怪物數量:", 0)
    for instanceId, monsterData in pairs(activeMonsters) do
        print("   -", instanceId, ":", monsterData.monsterId)
    end
end

-- 執行測試
local function runTests()
    if not checkWorkspaceModels() then
        print("❌ 模型檢查失敗，請確保 Workspace.Pet.Bacterial Virus 存在")
        return
    end
    
    local monsterSpawned, monsterName = testMonsterSpawn()
    local petSummoned, petName = testPetSummon()
    
    checkActiveMonsters()
    
    if petSummoned and monsterSpawned then
        testPetCombatAI(petName, monsterName)
        
        print("\n=== 測試完成 ===")
        print("✅ 基本功能測試完成")
        print("💡 請手動觀察:")
        print("   1. 寵物是否在玩家身邊")
        print("   2. 怪物是否在玩家右側")
        print("   3. 寵物是否會移動向怪物")
        print("   4. 是否有攻擊特效")
        print("   5. 怪物血量是否減少")
        
    else
        print("\n❌ 基本功能測試失敗")
        if not petSummoned then
            print("   - 寵物召喚失敗")
        end
        if not monsterSpawned then
            print("   - 怪物生成失敗")
        end
    end
end

-- 運行測試
runTests()

print("\n=== 手動調試命令 ===")
print("-- 檢查 Workspace 中的怪物:")
print("for _, model in pairs(workspace:GetChildren()) do")
print("    if model:IsA('Model') and string.find(model.Name, '_') then")
print("        print('Model:', model.Name)")
print("    end")
print("end")

print("\n-- 強制生成怪物:")
print("local MonsterService = require(game.ServerScriptService.Services.MonsterService)")
print("local player = game.Players:GetPlayers()[1]")
print("local pos = player.Character.HumanoidRootPart.Position + Vector3.new(5, 0, 0)")
print("MonsterService:SpawnMonster(player, 'bacterial_virus', pos)")

print("\n-- 檢查寵物是否能找到怪物:")
print("-- 觀察控制台輸出中的寵物AI信息")

print("\n=== 測試腳本完成 ===")

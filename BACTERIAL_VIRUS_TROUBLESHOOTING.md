# Bacterial Virus 問題排除指南

## 問題描述
- ✅ 寵物圖鑑中顯示 Bacterial Virus
- ❌ 無法召喚寵物進行戰鬥
- ❌ 怪物生成還是舊的怪物

## 可能原因分析

### 1. 玩家數據問題
**問題**: 玩家實際上沒有擁有 bacterial_virus 寵物
**原因**: 只是在配置中看到，但玩家數據中沒有

### 2. 模型路徑問題
**問題**: `workspace.Pet.Bacterial Virus` 不存在或結構不正確
**原因**: 模型位置錯誤或缺少必要組件

### 3. 怪物選擇邏輯問題
**問題**: MonsterService 沒有包含新怪物在隨機選擇中
**原因**: 怪物等級配置或選擇邏輯問題

## 診斷步驟

### 第一步：運行診斷腳本
在服務器控制台執行 `DEBUG_BACTERIAL_VIRUS.lua`：

```lua
-- 複製整個 DEBUG_BACTERIAL_VIRUS.lua 文件內容到控制台
```

### 第二步：檢查關鍵點
1. **模型存在性**: `workspace.Pet.Bacterial Virus` 是否存在
2. **玩家數據**: 玩家是否真的擁有該寵物
3. **配置正確性**: PetConfig 和 MonsterConfig 是否正確加載

### 第三步：快速修復
如果診斷發現問題，運行 `QUICK_FIX_BACTERIAL_VIRUS.lua`

## 修復方案

### 方案1：給玩家添加寵物（最常見問題）
```lua
-- 在服務器控制台執行
local player = game.Players:GetPlayers()[1] -- 或指定玩家名
local DataService = require(game.ServerScriptService.Services.DataService)
local data = DataService:GetPlayerData(player)

-- 添加寵物
data.pets["bacterial_virus"] = {
    id = "bacterial_virus",
    level = 1,
    experience = 0,
    isShiny = false,
    isActive = false,
    obtainedAt = os.time(),
}

-- 添加到發現列表
table.insert(data.petDex.discovered, "bacterial_virus")

-- 保存數據
DataService:SavePlayerData(player)
print("✅ 寵物已添加")
```

### 方案2：使用管理員命令
在遊戲聊天中輸入（需要是管理員）：
```
/give_pet [玩家名] bacterial_virus
/spawn_monster bacterial_virus
```

### 方案3：檢查模型結構
```lua
-- 檢查模型是否存在且結構正確
local model = workspace.Pet:FindFirstChild("Bacterial Virus")
if model then
    print("✅ 模型存在")
    -- 檢查是否有必要的組件
    local humanoid = model:FindFirstChildOfClass("Humanoid")
    local rootPart = model:FindFirstChild("HumanoidRootPart") or model.PrimaryPart
    
    print("Humanoid:", humanoid and "存在" or "缺失")
    print("RootPart:", rootPart and "存在" or "缺失")
else
    print("❌ 模型不存在，請檢查 Workspace.Pet.Bacterial Virus")
end
```

### 方案4：強制生成怪物測試
```lua
-- 強制生成 bacterial_virus 怪物
local MonsterService = require(game.ServerScriptService.Services.MonsterService)
local player = game.Players:GetPlayers()[1]
if player and player.Character then
    local pos = player.Character.HumanoidRootPart.Position + Vector3.new(10, 0, 0)
    MonsterService:SpawnMonster(player, "bacterial_virus", pos)
    print("👹 怪物已生成")
end
```

## 已實施的修復

### 1. 數據服務更新
- ✅ 默認給新玩家添加 bacterial_virus 寵物
- ✅ 自動添加到發現列表

### 2. 寵物服務改進
- ✅ 支援預製模型加載
- ✅ 自動添加必要的 Humanoid 組件
- ✅ 正確設置 HumanoidRootPart

### 3. 怪物服務改進
- ✅ 支援預製模型加載
- ✅ 自動設置怪物屬性

### 4. 管理員命令系統
- ✅ `/give_pet [玩家名] [寵物ID]`
- ✅ `/spawn_monster [怪物ID]`
- ✅ `/admin_help`

## 測試流程

### 1. 寵物召喚測試
1. 確認玩家擁有寵物
2. 打開寵物圖鑑
3. 點擊 Bacterial Virus 卡片
4. 點擊召喚按鈕
5. 檢查是否有寵物出現在玩家身邊

### 2. 怪物生成測試
1. 使用管理員命令生成怪物
2. 檢查是否使用了正確的模型
3. 測試怪物AI和戰鬥

### 3. 戰鬥系統測試
1. 召喚寵物
2. 生成怪物
3. 測試玩家攻擊怪物
4. 測試寵物攻擊怪物

## 常見錯誤和解決方案

### 錯誤1: "Player doesn't own pet"
**解決**: 使用方案1給玩家添加寵物

### 錯誤2: "Preset model not found"
**解決**: 檢查 `workspace.Pet.Bacterial Virus` 是否存在

### 錯誤3: 寵物召喚後沒有出現
**解決**: 檢查模型結構，確保有 HumanoidRootPart

### 錯誤4: 怪物還是舊的
**解決**: 使用 `/spawn_monster bacterial_virus` 強制生成

## 預防措施

### 1. 模型要求
- 確保模型在 `workspace.Pet.Bacterial Virus`
- 模型應該有合適的 Part 結構
- 建議設置 PrimaryPart

### 2. 數據一致性
- 新玩家自動獲得測試寵物
- 配置文件保持同步

### 3. 調試工具
- 使用 `/debug on` 啟用調試信息
- 定期檢查服務器控制台輸出

## 聯繫支援

如果問題仍然存在：
1. 運行完整的診斷腳本
2. 記錄控制台輸出
3. 檢查模型結構
4. 確認管理員權限設置

## 更新日誌

- **2025-01-30**: 初始問題報告和修復方案
- **2025-01-30**: 添加診斷腳本和快速修復工具
- **2025-01-30**: 實施管理員命令系統

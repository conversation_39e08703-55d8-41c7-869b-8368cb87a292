--[[
    測試 Bacterial Virus 修復
    在服務器控制台執行這個腳本來測試修復效果
]]

local Players = game:GetService("Players")

print("=== 測試 Bacterial Virus 修復 ===")

-- 獲取第一個玩家
local player = Players:GetPlayers()[1]
if not player then
    print("❌ 沒有玩家在線")
    return
end

print("🧪 測試玩家:", player.Name)

-- 1. 確保玩家有寵物
local function ensurePetOwnership()
    print("\n1. 檢查寵物擁有權...")
    
    local DataService = require(game.ServerScriptService.Services.DataService)
    local playerData = DataService:GetPlayerData(player)
    
    if not playerData.pets["bacterial_virus"] then
        print("🔧 添加 bacterial_virus 寵物...")
        playerData.pets["bacterial_virus"] = {
            id = "bacterial_virus",
            level = 1,
            experience = 0,
            isShiny = false,
            isActive = false,
            obtainedAt = os.time(),
        }
        
        if not table.find(playerData.petDex.discovered, "bacterial_virus") then
            table.insert(playerData.petDex.discovered, "bacterial_virus")
        end
        
        DataService:SavePlayerData(player)
        print("✅ 寵物已添加")
    else
        print("✅ 玩家已擁有 bacterial_virus 寵物")
    end
end

-- 2. 測試寵物召喚
local function testPetSummon()
    print("\n2. 測試寵物召喚...")
    
    local PetService = require(game.ServerScriptService.Services.PetService)
    
    -- 收回現有寵物
    PetService:_recallPet(player)
    wait(0.5)
    
    -- 召喚 bacterial_virus
    PetService:_summonPet(player, "bacterial_virus")
    wait(1)
    
    -- 檢查是否成功召喚
    local petFound = false
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "細菌病毒") then
            petFound = true
            print("✅ 寵物召喚成功:", model.Name)
            break
        end
    end
    
    if not petFound then
        print("❌ 寵物召喚失敗")
    end
    
    return petFound
end

-- 3. 測試怪物生成
local function testMonsterSpawn()
    print("\n3. 測試怪物生成...")
    
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 玩家角色不存在")
        return false
    end
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    local position = player.Character.HumanoidRootPart.Position + Vector3.new(15, 0, 0)
    
    -- 強制生成 bacterial_virus 怪物
    MonsterService:SpawnMonster(player, "bacterial_virus", position)
    wait(1)
    
    -- 檢查是否成功生成
    local monsterFound = false
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "bacterial_virus_") then
            monsterFound = true
            print("✅ 怪物生成成功:", model.Name)
            break
        end
    end
    
    if not monsterFound then
        print("❌ 怪物生成失敗")
    end
    
    return monsterFound
end

-- 4. 測試寵物戰鬥
local function testPetCombat()
    print("\n4. 測試寵物戰鬥...")
    
    -- 等待一段時間讓寵物AI檢測到怪物
    print("⏳ 等待寵物AI檢測怪物...")
    wait(5)
    
    -- 檢查是否有戰鬥日誌
    print("💡 請觀察控制台是否有以下信息:")
    print("   - '🐾 Pet found monster: ...'")
    print("   - '🐾 Pet [petId] attacking monster [instanceId]'")
    print("   - '🐾 Pet [petId] attacked monster [instanceId] for [damage] damage'")
    print("   - '☢️ Pet virus attack!'")
    
    return true
end

-- 5. 檢查模型結構
local function checkModelStructure()
    print("\n5. 檢查模型結構...")
    
    local petFolder = workspace:FindFirstChild("Pet")
    if not petFolder then
        print("❌ Pet 文件夾不存在")
        return false
    end
    
    local model = petFolder:FindFirstChild("Bacterial Virus")
    if not model then
        print("❌ Bacterial Virus 模型不存在")
        return false
    end
    
    print("✅ 模型存在:", model.Name)
    print("模型結構:")
    for _, child in pairs(model:GetChildren()) do
        print("  -", child.Name, "(" .. child.ClassName .. ")")
    end
    
    -- 檢查關鍵組件
    local humanoid = model:FindFirstChildOfClass("Humanoid")
    local rootPart = model:FindFirstChild("HumanoidRootPart") or model.PrimaryPart
    
    print("Humanoid:", humanoid and "✅ 存在" or "❌ 缺失")
    print("RootPart:", rootPart and "✅ 存在" or "❌ 缺失")
    
    return true
end

-- 執行測試
local function runTests()
    checkModelStructure()
    ensurePetOwnership()
    
    local petSummoned = testPetSummon()
    local monsterSpawned = testMonsterSpawn()
    
    if petSummoned and monsterSpawned then
        testPetCombat()
        
        print("\n=== 測試完成 ===")
        print("✅ 所有基本功能正常")
        print("💡 請觀察以下內容:")
        print("   1. 寵物是否出現在玩家身邊")
        print("   2. 怪物是否在玩家右側生成")
        print("   3. 寵物是否會自動攻擊怪物")
        print("   4. 是否有紫色毒液攻擊特效")
        print("   5. 怪物血量是否減少")
        
        print("\n🎮 手動測試建議:")
        print("   1. 打開寵物圖鑑，確認 Bacterial Virus 存在")
        print("   2. 點擊召喚按鈕測試手動召喚")
        print("   3. 使用 /spawn_monster bacterial_virus 生成更多怪物")
        print("   4. 觀察寵物是否會主動攻擊")
        
    else
        print("\n❌ 基本功能測試失敗")
        if not petSummoned then
            print("   - 寵物召喚失敗")
        end
        if not monsterSpawned then
            print("   - 怪物生成失敗")
        end
    end
end

-- 運行測試
runTests()

print("\n=== 調試命令 ===")
print("如果需要手動調試，可以使用以下命令:")
print("\n-- 重新召喚寵物:")
print("local PetService = require(game.ServerScriptService.Services.PetService)")
print("PetService:_summonPet(game.Players:GetPlayers()[1], 'bacterial_virus')")

print("\n-- 生成怪物:")
print("local MonsterService = require(game.ServerScriptService.Services.MonsterService)")
print("local player = game.Players:GetPlayers()[1]")
print("local pos = player.Character.HumanoidRootPart.Position + Vector3.new(10, 0, 0)")
print("MonsterService:SpawnMonster(player, 'bacterial_virus', pos)")

print("\n-- 檢查活躍怪物:")
print("local MonsterService = require(game.ServerScriptService.Services.MonsterService)")
print("local monsters = MonsterService:GetActiveMonsters()")
print("for id, data in pairs(monsters) do")
print("    print('Monster:', id, data.monsterId)")
print("end")

print("\n-- 啟用戰鬥調試:")
print("/debug combat on")

print("\n=== 測試腳本完成 ===")

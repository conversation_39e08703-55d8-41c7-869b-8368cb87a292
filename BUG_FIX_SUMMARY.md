# 主角攻擊怪物BUG修復總結

## 問題描述
主角攻擊怪物時沒有效果（怪物不會減血），但寵物攻擊卻有效果。

## 問題根因
客戶端和服務端之間的參數不匹配：

1. **客戶端**: `CombatController:_findNearestMonster()` 返回的是 `instanceId`
2. **服務端**: `CombatService:_isInAttackRange()` 期望的參數名為 `monsterId`，但實際查找時使用 `monsters[monsterId]`
3. **數據結構**: `MonsterService:GetActiveMonsters()` 返回的是以 `instanceId` 為鍵的字典

## 修復內容

### 1. 更新 CombatService.Client:AttackMonster
```lua
-- 修復前
function CombatService.Client:AttackMonster(player, monsterId)
    -- 使用 monsterId 查找怪物

-- 修復後  
function CombatService.Client:AttackMonster(player, instanceId)
    -- 使用 instanceId 查找怪物
```

### 2. 更新 _isInAttackRange 方法
```lua
-- 修復前
function CombatService:_isInAttackRange(player, monsterId)
    local monsterData = monsters[monsterId]  -- 找不到怪物

-- 修復後
function CombatService:_isInAttackRange(player, instanceId)
    local monsterData = monsters[instanceId]  -- 正確找到怪物
```

### 3. 同步更新寵物攻擊相關方法
- `CombatService.Client:PetAttackMonster`
- `CombatService:PetAttackMonster`  
- `CombatService:_isPetInAttackRange`

## 修復的文件
- `src/server/Services/CombatService.lua`

## 測試方法

### 測試步驟
1. 進入遊戲
2. 生成怪物（如果沒有自動生成）
3. 點擊"⚔️ 攻擊"按鈕攻擊怪物
4. 觀察怪物血量條是否減少
5. 觀察是否有傷害數字顯示
6. 確認怪物被擊殺後給予獎勵

### 預期結果
- ✅ 主角攻擊怪物時，怪物血量正常減少
- ✅ 顯示傷害數字特效
- ✅ 怪物血量條實時更新
- ✅ 怪物被擊殺後給予經驗和金幣獎勵
- ✅ 寵物攻擊功能保持正常

### 調試信息
如果需要調試，可以使用：
```
/debug combat on    # 啟用戰鬥調試
/debug on          # 啟用全局調試
```

## 技術細節

### 數據流程
1. **客戶端**: 玩家點擊攻擊按鈕
2. **客戶端**: `_findNearestMonster()` 返回 `instanceId`
3. **客戶端**: 調用 `CombatService:AttackMonster(instanceId)`
4. **服務端**: 檢查攻擊冷卻和距離
5. **服務端**: 調用 `MonsterService:DamageMonster(instanceId, damage, player)`
6. **服務端**: 更新怪物血量並通知客戶端
7. **客戶端**: 顯示攻擊效果和傷害數字

### 關鍵修復點
- 確保客戶端傳遞的 `instanceId` 與服務端查找使用的鍵一致
- 統一所有相關方法的參數命名
- 保持寵物攻擊系統的一致性

## 相關系統

### 怪物標識系統
- **模型名稱**: `monsterId_instanceId` (例如: "goblin_ABC123")
- **存儲鍵**: `instanceId` (例如: "ABC123")
- **查找方式**: 通過 `instanceId` 在 `activeMonsters` 中查找

### 攻擊範圍檢查
- **玩家攻擊範圍**: 10格
- **寵物攻擊範圍**: 8格
- **檢查方式**: 計算 3D 距離

## 後續改進建議

### 短期改進
1. 添加更詳細的錯誤日誌
2. 實現攻擊失敗的用戶反饋
3. 添加攻擊範圍的視覺指示

### 長期改進
1. 統一所有系統的標識符命名規範
2. 實現更強大的錯誤處理機制
3. 添加自動化測試覆蓋戰鬥系統

## 結論
此修復解決了主角攻擊無效的核心問題，確保了戰鬥系統的完整性。修復後，主角和寵物的攻擊都能正常工作，為玩家提供完整的戰鬥體驗。

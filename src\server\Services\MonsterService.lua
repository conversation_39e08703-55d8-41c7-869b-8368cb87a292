--[[
	MonsterService - 怪物服務
	處理怪物生成、管理和AI行為
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local RunService = game:GetService("RunService")
local Players = game:GetService("Players")
local MonsterConfig = require(game:GetService("ReplicatedStorage").Shared.MonsterConfig)
local AIUpdateManager = require(game:GetService("ReplicatedStorage").Shared.AIUpdateManager)
local MonsterHealthUI = require(game:GetService("ReplicatedStorage").Shared.MonsterHealthUI)

local MonsterService = Knit.CreateService({
	Name = "MonsterService",
	Client = {
		MonsterSpawned = Knit.CreateSignal(),
		MonsterDied = Knit.CreateSignal(),
		MonsterAttack = Knit.CreateSignal(),
	}
})

-- 私有變量
local activeMonsters = {} -- 活躍的怪物
local monsterAI = {} -- 怪物AI狀態
local aiConnection

function MonsterService:KnitStart()
	print("👹 MonsterService started")

	-- 啟動優化的AI更新循環
	aiConnection = RunService.Heartbeat:Connect(function()
		AIUpdateManager.update()
	end)

	-- 監聽玩家離開事件
	Players.PlayerRemoving:Connect(function(player)
		self:_cleanupPlayerMonsters(player)
	end)
end

-- 客戶端請求生成怪物
function MonsterService.Client:SpawnMonster(player, monsterId, position)
	return self.Server:SpawnMonster(player, monsterId, position)
end

-- 生成怪物
function MonsterService:SpawnMonster(player, monsterId, position)
	if not monsterId then
		-- 根據玩家等級選擇合適的怪物
		local playerData = self.DataService:GetPlayerData(player)
		local playerLevel = playerData and playerData.level or 1
		monsterId = MonsterConfig.getMonsterByLevel(playerLevel)
	end

	-- 檢查怪物配置是否存在
	local monsterConfig = MonsterConfig.getMonster(monsterId)
	if not monsterConfig then
		warn("Invalid monster ID:", monsterId)
		return
	end

	print("👹 Spawning monster:", monsterId, "at position:", position)
	
	-- 創建怪物模型
	local monsterModel = self:_createMonsterModel(monsterId, position or Vector3.new(0, 10, 0))
	if not monsterModel then
		warn("Failed to create monster model")
		return
	end
	
	-- 生成唯一ID
	local instanceId = game:GetService("HttpService"):GenerateGUID(false)
	monsterModel.Name = monsterId .. "_" .. instanceId
	
	-- 存儲怪物數據
	activeMonsters[instanceId] = {
		model = monsterModel,
		config = monsterConfig,
		currentHealth = monsterConfig.stats.maxHealth,
		spawner = player,
		monsterId = monsterId,
		instanceId = instanceId
	}
	
	-- 初始化AI狀態
	monsterAI[instanceId] = {
		state = "idle", -- idle, chasing, attacking, dead
		target = nil,
		lastAttack = 0,
		wanderTarget = position,
		lastWander = 0
	}

	-- 註冊到AI更新管理器
	AIUpdateManager.registerEntity(instanceId, function(id, entity)
		self:_updateSingleMonsterAI(id, activeMonsters[id], monsterAI[id])
	end, position)

	-- 創建血量條
	MonsterHealthUI.createHealthBar(monsterModel, monsterConfig.stats.maxHealth, monsterConfig.stats.maxHealth)

	-- 通知所有客戶端
	self.Client.MonsterSpawned:FireAll(instanceId, monsterId, position)

	print("👹 Spawned monster:", monsterConfig.name, "at", position)
	return instanceId
end

-- 創建怪物模型
function MonsterService:_createMonsterModel(monsterId, position)
	local config = MonsterConfig.getMonster(monsterId)
	if not config then return nil end

	-- 檢查是否有預製模型
	if config.appearance.modelName then
		local sourceModel = workspace.Pet:FindFirstChild(config.appearance.modelName)
		if sourceModel then
			-- 複製預製模型
			local model = sourceModel:Clone()
			model.Name = monsterId .. "_" .. game:GetService("HttpService"):GenerateGUID(false)

			-- 確保有 Humanoid
			local humanoid = model:FindFirstChildOfClass("Humanoid")
			if not humanoid then
				humanoid = Instance.new("Humanoid")
				humanoid.Parent = model
			end

			-- 設置怪物屬性
			humanoid.MaxHealth = config.stats.maxHealth
			humanoid.Health = config.stats.maxHealth
			humanoid.WalkSpeed = config.stats.speed

			-- 確保有 HumanoidRootPart
			local rootPart = model:FindFirstChild("HumanoidRootPart")
			if not rootPart then
				rootPart = model.PrimaryPart or model:FindFirstChildOfClass("Part")
				if rootPart then
					rootPart.Name = "HumanoidRootPart"
				else
					-- 創建一個新的 HumanoidRootPart
					rootPart = Instance.new("Part")
					rootPart.Name = "HumanoidRootPart"
					rootPart.Size = Vector3.new(2, 2, 1)
					rootPart.Transparency = 1
					rootPart.CanCollide = false
					rootPart.Parent = model
				end
			end

			-- 設置 PrimaryPart
			model.PrimaryPart = rootPart

			-- 設置位置
			model:SetPrimaryPartCFrame(CFrame.new(position))

			print("👹 Created monster from preset model:", config.name)
			return model
		else
			warn("⚠️ Preset monster model not found:", config.appearance.modelName)
		end
	end

	-- 創建程序化怪物模型（原有邏輯）
	local model = Instance.new("Model")
	model.Name = monsterId
	
	-- 創建身體
	local body = Instance.new("Part")
	body.Name = "Body"
	body.Size = config.appearance.size
	body.Color = config.appearance.color
	body.Material = config.appearance.material
	body.TopSurface = Enum.SurfaceType.Smooth
	body.BottomSurface = Enum.SurfaceType.Smooth
	body.CanCollide = true
	body.Parent = model
	
	-- 設置形狀
	if config.appearance.shape == "Ball" then
		body.Shape = Enum.PartType.Ball
	end
	
	-- 創建 Humanoid
	local humanoid = Instance.new("Humanoid")
	humanoid.MaxHealth = config.stats.maxHealth
	humanoid.Health = config.stats.maxHealth
	humanoid.WalkSpeed = config.stats.speed
	humanoid.Parent = model
	
	-- 創建 HumanoidRootPart
	local rootPart = Instance.new("Part")
	rootPart.Name = "HumanoidRootPart"
	rootPart.Size = Vector3.new(2, 2, 1)
	rootPart.Transparency = 1
	rootPart.CanCollide = false
	rootPart.Anchored = false
	rootPart.Parent = model
	
	-- 焊接身體到根部件
	local bodyWeld = Instance.new("WeldConstraint")
	bodyWeld.Part0 = rootPart
	bodyWeld.Part1 = body
	bodyWeld.Parent = rootPart
	
	-- 設置主要部件
	model.PrimaryPart = rootPart
	
	-- 添加怪物標籤
	local monsterIdValue = Instance.new("StringValue")
	monsterIdValue.Name = "MonsterId"
	monsterIdValue.Value = monsterId
	monsterIdValue.Parent = model
	
	-- 設置位置
	model:SetPrimaryPartCFrame(CFrame.new(position))
	
	-- 添加到workspace
	model.Parent = workspace
	
	return model
end

-- 更新怪物AI
function MonsterService:_updateMonsterAI()
	for instanceId, aiState in pairs(monsterAI) do
		local monsterData = activeMonsters[instanceId]
		if not monsterData or not monsterData.model.Parent then
			-- 清理無效的怪物
			activeMonsters[instanceId] = nil
			monsterAI[instanceId] = nil
			continue
		end
		
		self:_updateSingleMonsterAI(instanceId, monsterData, aiState)
	end
end

-- 更新單個怪物AI
function MonsterService:_updateSingleMonsterAI(instanceId, monsterData, aiState)
	if not monsterData or not aiState then return end

	local model = monsterData.model
	local config = monsterData.config
	local rootPart = model:FindFirstChild("HumanoidRootPart")
	local humanoid = model:FindFirstChild("Humanoid")

	if not rootPart or not humanoid then return end

	local currentTime = tick()
	local monsterPos = rootPart.Position

	-- 更新AI管理器中的位置
	AIUpdateManager.updateEntityPosition(instanceId, monsterPos)
	
	-- 尋找最近的玩家
	local nearestPlayer = nil
	local nearestDistance = math.huge
	
	for _, player in pairs(Players:GetPlayers()) do
		if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
			local distance = (player.Character.HumanoidRootPart.Position - monsterPos).Magnitude
			if distance < nearestDistance then
				nearestDistance = distance
				nearestPlayer = player
			end
		end
	end
	
	-- AI狀態機
	if aiState.state == "idle" then
		if nearestPlayer and nearestDistance <= config.behavior.chaseRange then
			aiState.state = "chasing"
			aiState.target = nearestPlayer
		else
			-- 隨機遊蕩
			if currentTime - aiState.lastWander > 3 then
				local wanderPos = aiState.wanderTarget + Vector3.new(
					math.random(-config.behavior.wanderRadius, config.behavior.wanderRadius),
					0,
					math.random(-config.behavior.wanderRadius, config.behavior.wanderRadius)
				)
				humanoid:MoveTo(wanderPos)
				aiState.lastWander = currentTime
			end
		end
	elseif aiState.state == "chasing" then
		if not aiState.target or not aiState.target.Character then
			aiState.state = "idle"
			aiState.target = nil
		else
			local targetPos = aiState.target.Character.HumanoidRootPart.Position
			local distance = (targetPos - monsterPos).Magnitude
			
			if distance > config.behavior.chaseRange then
				aiState.state = "idle"
				aiState.target = nil
			elseif distance <= config.behavior.attackRange then
				aiState.state = "attacking"
			else
				humanoid:MoveTo(targetPos)
			end
		end
	elseif aiState.state == "attacking" then
		if not aiState.target or not aiState.target.Character then
			aiState.state = "idle"
			aiState.target = nil
		else
			local targetPos = aiState.target.Character.HumanoidRootPart.Position
			local distance = (targetPos - monsterPos).Magnitude
			
			if distance > config.behavior.attackRange then
				aiState.state = "chasing"
			elseif currentTime - aiState.lastAttack >= config.behavior.attackCooldown then
				self:_performAttack(instanceId, aiState.target)
				aiState.lastAttack = currentTime
			end
		end
	end
end

-- 執行攻擊
function MonsterService:_performAttack(instanceId, target)
	local monsterData = activeMonsters[instanceId]
	if not monsterData or not target.Character then return end
	
	local damage = monsterData.config.stats.attack
	
	-- 通知戰鬥服務處理傷害
	local CombatService = Knit.GetService("CombatService")
	if CombatService then
		CombatService:DealDamage(target, damage, "monster", instanceId)
	end
	
	-- 通知客戶端播放攻擊動畫
	self.Client.MonsterAttack:FireAll(instanceId, target.UserId)
	
	print("👹 Monster", instanceId, "attacked", target.Name, "for", damage, "damage")
end

-- 怪物受到傷害
function MonsterService:DamageMonster(instanceId, damage, attacker)
	local monsterData = activeMonsters[instanceId]
	if not monsterData then return false end

	monsterData.currentHealth = math.max(0, monsterData.currentHealth - damage)

	-- 更新血量條
	if monsterData.model then
		MonsterHealthUI.updateHealthBar(
			monsterData.model,
			monsterData.currentHealth,
			monsterData.config.stats.maxHealth,
			true -- 使用動畫
		)
	end

	if monsterData.currentHealth <= 0 then
		self:_killMonster(instanceId, attacker)
		return true
	end

	print("👹 Monster", instanceId, "took", damage, "damage. Health:", monsterData.currentHealth .. "/" .. monsterData.config.stats.maxHealth)
	return false
end

-- 殺死怪物
function MonsterService:_killMonster(instanceId, killer)
	local monsterData = activeMonsters[instanceId]
	if not monsterData then return end

	-- 給予獎勵
	if killer and killer:IsA("Player") then
		local DataService = Knit.GetService("DataService")
		if DataService then
			DataService:AddExperience(killer, monsterData.config.rewards.experience)
			DataService:AddCoins(killer, monsterData.config.rewards.coins)
		end
	end

	-- 從AI管理器中移除
	AIUpdateManager.unregisterEntity(instanceId)

	-- 移除血量條（帶動畫效果）
	if monsterData.model then
		MonsterHealthUI.removeHealthBar(monsterData.model)
	end

	-- 通知客戶端
	self.Client.MonsterDied:FireAll(instanceId)

	-- 延遲銷毀怪物，讓血量條動畫播放完成
	task.wait(0.5)
	if monsterData.model then
		monsterData.model:Destroy()
	end

	activeMonsters[instanceId] = nil
	monsterAI[instanceId] = nil

	print("👹 Monster", instanceId, "died")
end

-- 清理玩家的怪物
function MonsterService:_cleanupPlayerMonsters(player)
	for instanceId, monsterData in pairs(activeMonsters) do
		if monsterData.spawner == player then
			if monsterData.model then
				monsterData.model:Destroy()
			end
			activeMonsters[instanceId] = nil
			monsterAI[instanceId] = nil
		end
	end
end

-- 獲取活躍怪物
function MonsterService:GetActiveMonsters()
	return activeMonsters
end

return MonsterService

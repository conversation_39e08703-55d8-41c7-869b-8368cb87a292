--[[
    快速修復 Bacterial Virus 問題
    在服務器控制台執行這個腳本
]]

local Players = game:GetService("Players")

print("=== 快速修復 Bacterial Virus 開始 ===")

-- 1. 檢查並修復第一個玩家的寵物數據
local function fixPlayerPets()
    local player = Players:GetPlayers()[1]
    if not player then
        print("❌ 沒有玩家在線")
        return false
    end
    
    print("🔧 修復玩家:", player.Name)
    
    local DataService = require(game.ServerScriptService.Services.DataService)
    local playerData = DataService:GetPlayerData(player)
    
    if not playerData then
        print("❌ 無法獲取玩家數據")
        return false
    end
    
    -- 添加 bacterial_virus 寵物
    playerData.pets["bacterial_virus"] = {
        id = "bacterial_virus",
        level = 1,
        experience = 0,
        isShiny = false,
        isActive = false,
        obtainedAt = os.time(),
    }
    
    -- 添加到發現列表
    if not table.find(playerData.petDex.discovered, "bacterial_virus") then
        table.insert(playerData.petDex.discovered, "bacterial_virus")
    end
    
    -- 保存數據
    DataService:SavePlayerData(player)
    
    print("✅ 寵物數據已修復")
    return true, player
end

-- 2. 測試寵物召喚
local function testPetSummon(player)
    print("🐾 測試寵物召喚...")
    
    local PetService = require(game.ServerScriptService.Services.PetService)
    
    -- 先收回現有寵物
    PetService:_recallPet(player)
    wait(0.5)
    
    -- 召喚新寵物
    PetService:_summonPet(player, "bacterial_virus")
    
    print("✅ 寵物召喚測試完成")
end

-- 3. 測試怪物生成
local function testMonsterSpawn(player)
    print("👹 測試怪物生成...")
    
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 玩家角色不存在")
        return
    end
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    local position = player.Character.HumanoidRootPart.Position + Vector3.new(10, 0, 0)
    
    MonsterService:SpawnMonster(player, "bacterial_virus", position)
    
    print("✅ 怪物生成測試完成")
end

-- 4. 檢查模型
local function checkModel()
    print("🔍 檢查模型...")
    
    local petFolder = workspace:FindFirstChild("Pet")
    if not petFolder then
        print("❌ Pet 文件夾不存在")
        return false
    end
    
    local model = petFolder:FindFirstChild("Bacterial Virus")
    if not model then
        print("❌ Bacterial Virus 模型不存在")
        print("Pet 文件夾中的模型:")
        for _, child in pairs(petFolder:GetChildren()) do
            print("  -", child.Name)
        end
        return false
    end
    
    print("✅ 模型存在:", model.Name)
    print("模型結構:")
    for _, child in pairs(model:GetChildren()) do
        print("  -", child.Name, "(" .. child.ClassName .. ")")
    end
    
    return true
end

-- 5. 檢查配置
local function checkConfigs()
    print("⚙️ 檢查配置...")
    
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    local PetConfig = require(ReplicatedStorage.Shared.PetConfig)
    local MonsterConfig = require(ReplicatedStorage.Shared.MonsterConfig)
    
    local petConfig = PetConfig.getPet("bacterial_virus")
    local monsterConfig = MonsterConfig.getMonster("bacterial_virus")
    
    if petConfig then
        print("✅ 寵物配置存在")
    else
        print("❌ 寵物配置不存在")
    end
    
    if monsterConfig then
        print("✅ 怪物配置存在")
    else
        print("❌ 怪物配置不存在")
    end
    
    return petConfig and monsterConfig
end

-- 執行修復流程
local function runFix()
    print("\n1. 檢查配置...")
    if not checkConfigs() then
        print("❌ 配置檢查失敗，請檢查 PetConfig 和 MonsterConfig")
        return
    end
    
    print("\n2. 檢查模型...")
    if not checkModel() then
        print("❌ 模型檢查失敗，請確保 Workspace.Pet.Bacterial Virus 存在")
        return
    end
    
    print("\n3. 修復玩家數據...")
    local success, player = fixPlayerPets()
    if not success then
        print("❌ 玩家數據修復失敗")
        return
    end
    
    print("\n4. 測試寵物召喚...")
    testPetSummon(player)
    
    print("\n5. 測試怪物生成...")
    testMonsterSpawn(player)
    
    print("\n✅ 修復完成！")
    print("現在玩家應該可以:")
    print("  - 在寵物圖鑑中看到 Bacterial Virus")
    print("  - 成功召喚 Bacterial Virus 寵物")
    print("  - 看到生成的 Bacterial Virus 怪物")
end

-- 運行修復
runFix()

print("\n=== 手動命令 ===")
print("如果自動修復失敗，可以手動執行以下命令:")
print("\n-- 給玩家添加寵物:")
print("local player = game.Players:GetPlayers()[1]")
print("local DataService = require(game.ServerScriptService.Services.DataService)")
print("local data = DataService:GetPlayerData(player)")
print("data.pets['bacterial_virus'] = {id='bacterial_virus', level=1, experience=0, isShiny=false, isActive=false, obtainedAt=os.time()}")
print("table.insert(data.petDex.discovered, 'bacterial_virus')")
print("DataService:SavePlayerData(player)")

print("\n-- 召喚寵物:")
print("local PetService = require(game.ServerScriptService.Services.PetService)")
print("PetService:_summonPet(player, 'bacterial_virus')")

print("\n-- 生成怪物:")
print("local MonsterService = require(game.ServerScriptService.Services.MonsterService)")
print("local pos = player.Character.HumanoidRootPart.Position + Vector3.new(10, 0, 0)")
print("MonsterService:SpawnMonster(player, 'bacterial_virus', pos)")

print("\n=== 管理員聊天命令 ===")
print("在遊戲中輸入以下命令 (需要是管理員):")
print("/give_pet [玩家名] bacterial_virus")
print("/spawn_monster bacterial_virus")
print("/admin_help")

print("\n=== 快速修復完成 ===")

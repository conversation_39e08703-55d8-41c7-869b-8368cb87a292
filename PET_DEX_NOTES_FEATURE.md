# 寵物圖鑑備註功能說明

## 概述
為寵物圖鑑添加了詳細的備註功能，玩家可以點擊寵物卡片查看豐富的寵物資訊，包括來源、能力值、特殊技能等詳細內容。

## 功能特色

### 🎯 交互式詳情面板
- **左右分欄布局**: 左側4×4寵物網格，右側詳情面板
- **點擊查看**: 點擊任意寵物卡片即可查看詳細資訊
- **選中狀態**: 被選中的寵物卡片會高亮顯示
- **響應式更新**: 實時反映寵物狀態變化

### 📊 詳細資訊展示

#### 1. 寵物頭像區域
- **大型圖標**: 80×80px 寵物表情符號
- **基本信息**: 名稱、等級、種族、屬性
- **稀有度標籤**: 彩色稀有度標識
- **視覺狀態**: 已擁有/未擁有的視覺區分

#### 2. 基礎屬性面板
- **可視化屬性條**: 生命值、攻擊力、防禦力、速度
- **彩色進度條**: 不同屬性使用不同顏色
- **數值顯示**: 精確的屬性數值
- **比例顯示**: 相對於最大值的百分比

#### 3. 技能描述
- **詳細說明**: 寵物的技能和特色描述
- **格式化文字**: 清晰易讀的排版

#### 4. 詳細備註（已擁有寵物）
- **🌍 來源**: 寵物的原產地
- **🏠 棲息地**: 自然棲息環境
- **😊 性格**: 寵物的性格特徵
- **✨ 特殊能力**: 獨特技能列表
- **🔄 進化潛力**: 進化可能性

#### 5. 獲取方式
- **🎁 獲取方法**: 如何獲得這隻寵物
- **遭遇機率**: 獲得的稀有度百分比

## 技術實現

### 數據結構擴展
```lua
-- 在 PetConfig.lua 中為每隻寵物添加 notes 字段
notes = {
    origin = "火山地帶",
    habitat = "炎熱的火山口附近，以火焰能量為食",
    personality = "活潑好動，忠誠度高，喜歡溫暖的環境",
    obtainMethod = "在火山區域探索時有機會遇到",
    rarity_chance = "15%",
    special_traits = {"火焰免疫", "夜視能力"},
    evolution_potential = "可進化為烈焰狐王",
}
```

### UI架構
```lua
-- 主要組件結構
PetDexFrame (1200×700px)
├── PetCardsContainer (左側60%)
│   └── UIGridLayout (4×4網格)
└── PetDetailsPanel (右側40%)
    ├── PetHeader (寵物頭像和基本信息)
    ├── StatsSection (屬性面板)
    ├── AbilitiesSection (技能描述)
    ├── NotesSection (詳細備註)
    └── ObtainSection (獲取方式)
```

### 狀態管理
```lua
-- 新增狀態變量
local selectedPetForDetails = Value(nil)

-- 響應式UI更新
Computed(function()
    local selectedPet = selectedPetForDetails:get()
    if selectedPet then
        return self:_createPetDetailsContent(selectedPet)
    else
        return self:_createEmptyDetailsContent()
    end
end)
```

## 用戶體驗設計

### 🎨 視覺設計
- **一致的配色**: 使用統一的深色主題
- **清晰的層次**: 不同信息區域明確分隔
- **彩色標識**: 屬性、稀有度使用對應顏色
- **選中反饋**: 藍色高亮和邊框指示

### 🖱️ 交互設計
- **直觀操作**: 點擊即可查看詳情
- **即時反饋**: 選中狀態立即顯示
- **滾動支援**: 詳情面板支援滾動查看
- **空狀態提示**: 未選中時顯示操作提示

### 📱 信息架構
- **漸進式揭示**: 基本信息→詳細信息→獲取方式
- **權限控制**: 未擁有寵物顯示有限信息
- **完整性**: 已擁有寵物顯示全部詳情

## 新增的寵物資訊

### 火焰狐狸
- **來源**: 火山地帶
- **棲息地**: 炎熱的火山口附近
- **性格**: 活潑好動，忠誠度高
- **特殊能力**: 火焰免疫、夜視能力
- **獲取方式**: 火山區域探索 (15%機率)

### 水晶龜
- **來源**: 深海水晶洞穴
- **棲息地**: 富含礦物質的深海區域
- **性格**: 沉穩冷靜，極具耐心
- **特殊能力**: 水下呼吸、水晶硬化、自我修復
- **獲取方式**: 深海探險或水晶洞穴挖掘 (8%機率)

### 風之鳥
- **來源**: 天空之城遺跡
- **棲息地**: 高空雲層中
- **性格**: 自由奔放，難以馴服
- **特殊能力**: 飛行能力、風暴召喚、瞬間移動
- **獲取方式**: 完成天空神殿試煉 (3%機率)

## 使用方法

### 基本操作
1. **打開圖鑑**: 點擊寵物UI中的"圖鑑"按鈕
2. **選擇寵物**: 點擊左側任意寵物卡片
3. **查看詳情**: 右側面板顯示詳細資訊
4. **滾動查看**: 使用滾動條查看完整內容
5. **召喚寵物**: 點擊卡片上的召喚按鈕

### 信息解讀
- **綠色屬性條**: 生命值
- **橙色屬性條**: 攻擊力  
- **藍色屬性條**: 防禦力
- **綠色屬性條**: 速度
- **彩色稀有度**: 普通(灰)、稀有(藍)、史詩(紫)、傳說(金)

## 開發者接口

### 添加新寵物備註
```lua
-- 在 PetConfig.lua 中為新寵物添加 notes
["new_pet_id"] = {
    -- 基本配置...
    notes = {
        origin = "寵物來源地",
        habitat = "棲息地描述",
        personality = "性格特徵",
        obtainMethod = "獲取方式",
        rarity_chance = "機率百分比",
        special_traits = {"特殊能力1", "特殊能力2"},
        evolution_potential = "進化描述",
    },
}
```

### 自定義詳情面板
```lua
-- 可以擴展 _createPetDetailsContent 函數
-- 添加新的信息區域或修改現有布局
```

## 未來擴展

### 短期改進
1. **3D模型預覽**: 替換表情符號為3D模型
2. **動畫效果**: 添加切換動畫
3. **音效反饋**: 點擊和切換音效
4. **收藏功能**: 標記喜愛的寵物

### 長期規劃
1. **比較功能**: 並排比較多隻寵物
2. **進化樹**: 顯示完整進化路線
3. **戰鬥模擬**: 預覽寵物戰鬥能力
4. **社交分享**: 分享寵物收藏

## 性能考量

### 優化措施
- **按需渲染**: 只渲染選中寵物的詳情
- **狀態緩存**: 避免重複計算
- **滾動優化**: 虛擬化長列表內容
- **圖片懶加載**: 未來3D模型的懶加載

### 記憶體管理
- **組件重用**: 詳情面板組件重用
- **數據清理**: 及時清理未使用的狀態
- **事件管理**: 正確的事件綁定和解綁

## 結論

寵物圖鑑備註功能大幅提升了寵物系統的深度和趣味性：

- ✅ **豐富的內容**: 每隻寵物都有詳細的背景故事
- ✅ **直觀的展示**: 清晰的信息架構和視覺設計
- ✅ **良好的交互**: 簡單易用的操作方式
- ✅ **可擴展性**: 易於添加新的寵物和信息類型

這個功能為玩家提供了更深入了解寵物的途徑，增強了收集的成就感和遊戲的沉浸感。

--[[
    調試傷害系統
    在服務器控制台執行
]]

local Players = game:GetService("Players")

print("=== 調試傷害系統 ===")

local player = Players:GetPlayers()[1]
if not player then
    print("❌ 沒有玩家在線")
    return
end

print("🧪 測試玩家:", player.Name)

-- 1. 檢查活躍怪物
local function checkActiveMonsters()
    print("\n1. 檢查活躍怪物...")
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    local activeMonsters = MonsterService:GetActiveMonsters()
    
    print("活躍怪物數量:", 0)
    local count = 0
    for instanceId, monsterData in pairs(activeMonsters) do
        count = count + 1
        print("   -", instanceId, ":", monsterData.monsterId, "血量:", monsterData.currentHealth .. "/" .. monsterData.config.stats.maxHealth)
    end
    
    if count == 0 then
        print("❌ 沒有活躍怪物")
        return nil
    end
    
    -- 返回第一個怪物的 instanceId
    for instanceId, _ in pairs(activeMonsters) do
        return instanceId
    end
end

-- 2. 檢查 Workspace 中的怪物模型
local function checkWorkspaceMonsters()
    print("\n2. 檢查 Workspace 中的怪物模型...")
    
    local monsters = {}
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "_") then
            local parts = string.split(model.Name, "_")
            if #parts >= 2 and string.find(parts[1], "bacterial") then
                local instanceId = parts[2]
                monsters[instanceId] = model
                print("   - 模型:", model.Name, "instanceId:", instanceId)
            end
        end
    end
    
    return monsters
end

-- 3. 生成測試怪物
local function spawnTestMonster()
    print("\n3. 生成測試怪物...")
    
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 玩家角色不存在")
        return nil
    end
    
    -- 清理舊怪物
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "bacterial_virus_") then
            print("🧹 清理舊怪物:", model.Name)
            model:Destroy()
        end
    end
    
    wait(0.5)
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    local position = player.Character.HumanoidRootPart.Position + Vector3.new(5, 0, 0)
    
    MonsterService:SpawnMonster(player, "bacterial_virus", position)
    wait(1)
    
    return checkActiveMonsters()
end

-- 4. 測試直接傷害
local function testDirectDamage(instanceId)
    print("\n4. 測試直接傷害...")
    
    if not instanceId then
        print("❌ 沒有 instanceId")
        return
    end
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    
    print("🎯 對怪物", instanceId, "造成 10 點傷害")
    local killed = MonsterService:DamageMonster(instanceId, 10, player)
    
    print("結果: 怪物", killed and "被擊殺" or "存活")
    
    -- 檢查血量
    local activeMonsters = MonsterService:GetActiveMonsters()
    local monsterData = activeMonsters[instanceId]
    if monsterData then
        print("當前血量:", monsterData.currentHealth .. "/" .. monsterData.config.stats.maxHealth)
    else
        print("怪物數據已移除")
    end
end

-- 5. 測試戰鬥服務攻擊
local function testCombatServiceAttack(instanceId)
    print("\n5. 測試戰鬥服務攻擊...")
    
    if not instanceId then
        print("❌ 沒有 instanceId")
        return
    end
    
    local CombatService = require(game.ServerScriptService.Services.CombatService)
    
    print("🗡️ 玩家攻擊怪物", instanceId)
    CombatService:AttackMonster(player, instanceId)
    
    wait(0.5)
    
    print("🐾 寵物攻擊怪物", instanceId)
    CombatService:PetAttackMonster(player, "bacterial_virus", instanceId)
end

-- 6. 檢查怪物血量條
local function checkHealthBar(instanceId)
    print("\n6. 檢查怪物血量條...")
    
    local workspaceMonsters = checkWorkspaceMonsters()
    local model = workspaceMonsters[instanceId]
    
    if not model then
        print("❌ 找不到怪物模型")
        return
    end
    
    -- 檢查血量條
    local healthBar = model:FindFirstChild("HealthBar")
    if healthBar then
        print("✅ 血量條存在")
        local healthFrame = healthBar:FindFirstChild("HealthFrame")
        if healthFrame then
            local healthFill = healthFrame:FindFirstChild("HealthFill")
            if healthFill then
                print("   血量條填充:", healthFill.Size.X.Scale * 100 .. "%")
            end
        end
    else
        print("❌ 血量條不存在")
    end
end

-- 執行測試
local function runTest()
    -- 檢查現有怪物
    local instanceId = checkActiveMonsters()
    checkWorkspaceMonsters()
    
    -- 如果沒有怪物，生成一個
    if not instanceId then
        instanceId = spawnTestMonster()
    end
    
    if instanceId then
        print("\n🎯 使用怪物 instanceId:", instanceId)
        
        -- 測試直接傷害
        testDirectDamage(instanceId)
        
        -- 檢查血量條
        checkHealthBar(instanceId)
        
        -- 測試戰鬥服務
        testCombatServiceAttack(instanceId)
        
        print("\n=== 測試完成 ===")
        print("💡 請觀察控制台輸出:")
        print("   - 是否有 '🩸 DamageMonster called' 信息")
        print("   - 是否有 '👹 Monster X took Y damage' 信息")
        print("   - 是否有 '⚔️ Player X attacked monster' 信息")
        print("   - 是否有 '🐾 Pet X attacked monster' 信息")
        
    else
        print("❌ 無法獲取怪物 instanceId")
    end
end

-- 運行測試
runTest()

print("\n=== 手動調試命令 ===")
print("-- 檢查活躍怪物:")
print("local MonsterService = require(game.ServerScriptService.Services.MonsterService)")
print("local monsters = MonsterService:GetActiveMonsters()")
print("for id, data in pairs(monsters) do")
print("    print('Monster:', id, data.monsterId, 'Health:', data.currentHealth)")
print("end")

print("\n-- 直接測試傷害:")
print("local instanceId = 'YOUR_INSTANCE_ID'")
print("MonsterService:DamageMonster(instanceId, 50, game.Players:GetPlayers()[1])")

print("\n-- 檢查 Workspace 怪物:")
print("for _, model in pairs(workspace:GetChildren()) do")
print("    if model:IsA('Model') and string.find(model.Name, 'bacterial_virus_') then")
print("        print('Monster model:', model.Name)")
print("        local humanoid = model:FindFirstChildOfClass('Humanoid')")
print("        if humanoid then")
print("            print('  Humanoid Health:', humanoid.Health, '/', humanoid.MaxHealth)")
print("        end")
print("    end")
print("end")

print("\n=== 調試完成 ===")

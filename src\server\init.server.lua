--[[
	Pet RPG - Server Initialization
	使用 Knit 框架初始化服務端
]]

-- 載入依賴
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

-- 載入所有服務
local Services = script.Services
for _, serviceModule in pairs(Services:GetChildren()) do
	if serviceModule:IsA("ModuleScript") then
		require(serviceModule)
	end
end

-- 啟動 Knit 服務端
Knit.Start():andThen(function()
	print("🎮 Pet RPG Server Started!")

	-- 確保服務依賴關係
	local DataService = Knit.GetService("DataService")
	local PetService = Knit.GetService("PetService")
	local MonsterService = Knit.GetService("MonsterService")
	local CombatService = Knit.GetService("CombatService")

	-- 設置服務間依賴
	PetService.DataService = DataService
	MonsterService.DataService = DataService
	CombatService.DataService = DataService

	print("⚔️ Combat system initialized!")

	-- 設置管理員命令
	local Players = game:GetService("Players")
	local ReplicatedStorage = game:GetService("ReplicatedStorage")
	local DebugConfig = require(ReplicatedStorage.Shared.DebugConfig)

	-- 管理員列表（替換為實際的管理員用戶名）
	local admins = {"YourUsername"} -- 請替換為您的用戶名

	local function isAdmin(player)
		for _, adminName in pairs(admins) do
			if player.Name == adminName then
				return true
			end
		end
		return false
	end

	-- 監聽玩家聊天
	Players.PlayerAdded:Connect(function(player)
		player.Chatted:Connect(function(message)
			-- 處理調試命令
			if DebugConfig.handleChatCommand(player, message) then
				return
			end

			-- 處理管理員命令
			if isAdmin(player) then
				local args = string.split(message:lower(), " ")

				if args[1] == "/give_pet" and args[2] then
					local targetPlayerName = args[2]
					local petId = args[3] or "bacterial_virus"

					local targetPlayer = Players:FindFirstChild(targetPlayerName)
					if targetPlayer then
						local playerData = DataService:GetPlayerData(targetPlayer)
						if playerData then
							playerData.pets[petId] = {
								id = petId,
								level = 1,
								experience = 0,
								isShiny = false,
								isActive = false,
								obtainedAt = os.time(),
							}

							-- 添加到發現列表
							if not table.find(playerData.petDex.discovered, petId) then
								table.insert(playerData.petDex.discovered, petId)
							end

							DataService:SavePlayerData(targetPlayer)
							print("✅ 已為玩家", targetPlayer.Name, "添加寵物:", petId)
						end
					else
						print("❌ 找不到玩家:", targetPlayerName)
					end
				elseif args[1] == "/spawn_monster" then
					local monsterId = args[2] or "bacterial_virus"
					if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
						local position = player.Character.HumanoidRootPart.Position + Vector3.new(10, 0, 0)
						MonsterService:SpawnMonster(player, monsterId, position)
						print("👹 已生成怪物:", monsterId)
					end
				elseif args[1] == "/admin_help" then
					print("管理員命令:")
					print("  /give_pet [玩家名] [寵物ID] - 給玩家添加寵物")
					print("  /spawn_monster [怪物ID] - 生成怪物")
					print("  /admin_help - 顯示此幫助")
				end
			end
		end)
	end)

end):catch(function(err)
	warn("❌ Server startup failed:", err)
end)

--[[
	Pet RPG - Server Initialization
	使用 Knit 框架初始化服務端
]]

-- 載入依賴
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)

-- 載入所有服務
local Services = script.Services
for _, serviceModule in pairs(Services:GetChildren()) do
	if serviceModule:IsA("ModuleScript") then
		require(serviceModule)
	end
end

-- 啟動 Knit 服務端
Knit.Start():andThen(function()
	print("🎮 Pet RPG Server Started!")

	-- 確保服務依賴關係
	local DataService = Knit.GetService("DataService")
	local PetService = Knit.GetService("PetService")
	local MonsterService = Knit.GetService("MonsterService")
	local CombatService = Knit.GetService("CombatService")

	-- 設置服務間依賴
	PetService.DataService = DataService
	MonsterService.DataService = DataService
	CombatService.DataService = DataService

	print("⚔️ Combat system initialized!")
end):catch(function(err)
	warn("❌ Server startup failed:", err)
end)

# 玩家血量系統說明

## 概述
玩家血量系統為遊戲提供了完整的血量顯示和反饋機制，包括視覺效果、音效提示和動畫反饋。

## 功能特色

### 🩸 血量條顯示
- **位置**: 遊戲界面左上角
- **信息**: 顯示當前血量/最大血量和百分比
- **顏色變化**: 
  - 綠色 (>60%): 健康狀態
  - 黃色 (30-60%): 受傷狀態  
  - 紅色 (<30%): 危險狀態

### ⚠️ 低血量警告系統
當玩家血量低於25%時自動觸發：
- 血量條紅色閃爍效果
- 警告音效播放
- 持續提醒直到血量恢復

### 💥 傷害反饋效果
玩家受到傷害時：
- 血量條震動動畫
- 紅色傷害數字浮動顯示
- 血量條平滑減少動畫
- 受傷音效播放

### 💚 治療反饋效果
玩家恢復血量時：
- 血量條綠色閃爍效果
- 綠色治療數字浮動顯示
- 血量條平滑增加動畫
- 治療音效播放

## UI組件結構

```
PlayerHealthContainer
├── HealthTitle (標題文字)
├── HealthBarBackground (血量條背景)
│   ├── HealthFill (血量填充條)
│   └── HealthText (血量數值文字)
└── HealthPercentage (血量百分比)
```

## 技術實現

### 響應式設計
- 使用 Fusion 框架實現響應式UI更新
- 血量變化時自動更新顏色和數值
- 流暢的動畫過渡效果

### 性能優化
- 使用特效對象池重用傷害/治療數字
- 智能更新機制避免不必要的重繪
- 低血量警告使用協程避免阻塞主線程

### 事件驅動
- 監聽 CombatService 的 CombatUpdate 事件
- 自動處理血量變化和相關效果
- 支援服務端權威的血量管理

## 使用方法

### 基本操作
1. **查看血量**: 血量條始終顯示在界面左上角
2. **測試治療**: 點擊"💚 治療"按鈕恢復25%血量
3. **戰鬥受傷**: 與怪物戰鬥時會自動受到傷害

### 開發者接口

#### 手動更新血量
```lua
local CombatController = Knit.GetController("CombatController")

-- 模擬受傷
healthState:set(currentHealth - damage)

-- 模擬治療
healthState:set(math.min(maxHealth, currentHealth + healing))
```

#### 自定義血量變化效果
```lua
-- 觸發血量變化處理
CombatController:_handleHealthChange(oldHealth, newHealth, maxHealth)

-- 顯示自定義傷害數字
CombatController:_showPlayerDamageNumber(damage)

-- 顯示自定義治療數字
CombatController:_showPlayerHealingNumber(healing)
```

#### 控制低血量警告
```lua
-- 手動開始警告
CombatController:_startLowHealthWarning()

-- 手動停止警告
CombatController:_stopLowHealthWarning()
```

## 配置選項

### 血量閾值
- **低血量警告**: 25% (可在代碼中調整)
- **顏色變化點**: 60% (綠→黃), 30% (黃→紅)

### 動畫設置
- **震動強度**: ±3像素 (受傷時)
- **數字浮動時間**: 1.5秒
- **閃爍週期**: 0.5秒 (低血量警告)

### 治療設置
- **測試治療量**: 25%最大血量
- **治療冷卻**: 無限制 (測試模式)

## 音效系統

### 音效類型
- `low_health`: 低血量警告音效
- `hurt`: 受傷音效
- `heal`: 治療音效 (可擴展)

### 音效配置
```lua
local soundIds = {
    low_health = "rbxasset://sounds/alert.wav",
    hurt = "rbxasset://sounds/impact_water.mp3",
    -- 可添加更多音效
}
```

## 擴展功能建議

### 短期改進
1. **血量恢復道具**: 添加血瓶等恢復道具
2. **護盾系統**: 臨時護盾機制
3. **血量上限提升**: 等級提升增加最大血量
4. **狀態效果**: 中毒、燃燒等持續傷害

### 長期規劃
1. **多種血量類型**: 魔力值、體力值等
2. **血量同步**: 多人遊戲中的血量同步
3. **戰鬥統計**: 傷害統計和戰鬥報告
4. **自定義UI**: 玩家可自定義血量條樣式

## 故障排除

### 常見問題

**Q: 血量條不顯示**
A: 檢查 CombatController 是否正確初始化，確保 Fusion 框架正常載入

**Q: 低血量警告不停止**
A: 檢查血量是否真的恢復到25%以上，或手動調用停止警告函數

**Q: 傷害數字不顯示**
A: 確保 EffectPool 已正確初始化，檢查玩家角色是否有 HumanoidRootPart

**Q: 音效不播放**
A: 檢查音效ID是否正確，確保遊戲音量設置正常

### 調試工具
```lua
-- 檢查當前血量狀態
print("Current Health:", healthState:get())
print("Max Health:", maxHealthState:get())
print("Low Health Warning:", CombatController.lowHealthWarning)

-- 強制更新血量條
CombatController:_handleHealthChange(0, healthState:get(), maxHealthState:get())
```

## 結論

玩家血量系統提供了完整的血量管理和視覺反饋機制，大大提升了遊戲的沉浸感和用戶體驗。系統設計靈活，易於擴展，為未來的功能開發奠定了良好基礎。

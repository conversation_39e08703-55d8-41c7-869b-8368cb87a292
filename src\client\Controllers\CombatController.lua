--[[
	CombatController - 戰鬥控制器
	處理客戶端戰鬥UI和輸入
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local RunService = game:GetService("RunService")
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)
local TargetingSystem = require(game:GetService("ReplicatedStorage").Shared.TargetingSystem)
local EffectPool = require(game:GetService("ReplicatedStorage").Shared.EffectPool)

local CombatController = Knit.CreateController({
	Name = "CombatController",
})

-- Fusion 組件
local New = Fusion.New
local Value = Fusion.Value
local Computed = Fusion.Computed
local OnEvent = Fusion.OnEvent
local Children = Fusion.Children

-- 私有變量
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
local camera = workspace.CurrentCamera

-- 狀態變量
local healthState = Value(100)
local maxHealthState = Value(100)
local attackCooldownState = Value(0) -- 攻擊冷卻進度 (0-1)
local isAttackOnCooldown = Value(false)
local combatUI = nil

function CombatController:KnitStart()
	print("⚔️ CombatController started")

	-- 初始化實例變量
	self.targetMonster = nil
	self.lastFoundTarget = nil
	self.lastAttackTime = 0
	self.attackCooldown = 1.5 -- 1.5秒攻擊冷卻
	
	-- 等待並獲取服務
	Knit.OnStart():andThen(function()
		print("🔥 Knit started, getting services...")
		self.CombatService = Knit.GetService("CombatService")
		self.MonsterService = Knit.GetService("MonsterService")
		print("🔥 Services obtained:", self.CombatService ~= nil, self.MonsterService ~= nil)

		-- 監聽戰鬥事件（客戶端信號）
		self.CombatService.TakeDamage:Connect(function(damage, source, sourceId)
			self:_showDamageEffect(damage, source)
		end)

		self.CombatService.CombatUpdate:Connect(function(currentHealth, maxHealth)
			healthState:set(currentHealth)
			maxHealthState:set(maxHealth)
		end)

		self.CombatService.AttackResult:Connect(function(monsterId, damage, killed)
			self:_showAttackEffect(monsterId, damage, killed)
		end)

		-- 監聽怪物事件（客戶端信號）
		self.MonsterService.MonsterSpawned:Connect(function(instanceId, monsterId, position)
			self:_onMonsterSpawned(instanceId, monsterId, position)
		end)

		self.MonsterService.MonsterDied:Connect(function(instanceId)
			self:_onMonsterDied(instanceId)
		end)

		self.MonsterService.MonsterAttack:Connect(function(instanceId, targetUserId)
			self:_onMonsterAttack(instanceId, targetUserId)
		end)

		print("🔥 All event connections established")
	end):catch(function(err)
		warn("❌ Failed to initialize CombatController services:", err)
	end)
	
	-- 創建戰鬥UI
	self:_createCombatUI()
	print("🔥 Combat UI created")

	-- 初始化特效池
	EffectPool.warmUp()
	print("🔥 Effect pool initialized")

	-- 為玩家裝備劍
	self:_ensurePlayerSword()

	-- 監聽角色重生事件，重新裝備劍
	player.CharacterAdded:Connect(function(character)
		character:WaitForChild("HumanoidRootPart")
		wait(1) -- 等待角色完全載入
		self:_ensurePlayerSword()
		print("⚔️ Sword re-equipped after respawn")
	end)
	
	-- 移除快捷鍵，只使用UI按鈕
	
	-- 啟動自動戰鬥循環
	RunService.Heartbeat:Connect(function()
		self:_updateCombat()
	end)
end

-- 創建戰鬥UI
function CombatController:_createCombatUI()
	combatUI = New "ScreenGui" {
		Name = "CombatUI",
		Parent = playerGui,
		
		[Children] = {
			-- 血量條
			New "Frame" {
				Name = "HealthBar",
				Size = UDim2.new(0, 200, 0, 20),
				Position = UDim2.new(0, 20, 0, 20),
				BackgroundColor3 = Color3.fromRGB(50, 50, 50),
				BorderSizePixel = 2,
				BorderColor3 = Color3.fromRGB(255, 255, 255),
				
				[Children] = {
					New "Frame" {
						Name = "HealthFill",
						Size = Computed(function()
							local current = healthState:get()
							local max = maxHealthState:get()
							local ratio = max > 0 and current / max or 0
							return UDim2.new(ratio, 0, 1, 0)
						end),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundColor3 = Color3.fromRGB(255, 100, 100),
						BorderSizePixel = 0,
					},
					
					New "TextLabel" {
						Name = "HealthText",
						Size = UDim2.new(1, 0, 1, 0),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,
						Text = Computed(function()
							return healthState:get() .. " / " .. maxHealthState:get()
						end),
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					}
				}
			},
			
			-- 右下角按鈕容器
			New "Frame" {
				Name = "ButtonContainer",
				Size = UDim2.new(0, 250, 0, 120),
				Position = UDim2.new(1, -270, 1, -140),
				BackgroundTransparency = 1,

				[Children] = {
					-- 攻擊按鈕容器
					New "Frame" {
						Name = "AttackButtonContainer",
						Size = UDim2.new(0, 110, 0, 50),
						Position = UDim2.new(0, 0, 0, 0),
						BackgroundTransparency = 1,

						[Children] = {
							-- 攻擊按鈕
							New "TextButton" {
								Name = "AttackButton",
								Size = UDim2.new(1, 0, 1, 0),
								Position = UDim2.new(0, 0, 0, 0),
								BackgroundColor3 = Computed(function()
									return isAttackOnCooldown:get() and Color3.fromRGB(150, 50, 50) or Color3.fromRGB(255, 100, 100)
								end),
								BorderSizePixel = 2,
								BorderColor3 = Color3.fromRGB(255, 255, 255),
								Text = Computed(function()
									return isAttackOnCooldown:get() and "⏳ 冷卻中" or "⚔️ 攻擊"
								end),
								TextColor3 = Computed(function()
									return isAttackOnCooldown:get() and Color3.fromRGB(200, 200, 200) or Color3.fromRGB(255, 255, 255)
								end),
								TextScaled = true,
								Font = Enum.Font.GothamBold,
								Active = Computed(function()
									return not isAttackOnCooldown:get()
								end),

								[OnEvent "Activated"] = function()
									if not isAttackOnCooldown:get() then
										print("🔥 Attack button clicked!")
										self:_performAttack()
									end
								end,

								[OnEvent "MouseButton1Click"] = function()
									if not isAttackOnCooldown:get() then
										print("🔥 Attack button MouseButton1Click!")
										self:_performAttack()
									end
								end
							},

							-- 冷卻進度條
							New "Frame" {
								Name = "CooldownOverlay",
								Size = Computed(function()
									local progress = attackCooldownState:get()
									return UDim2.new(1, 0, progress, 0)
								end),
								Position = UDim2.new(0, 0, 1, 0),
								AnchorPoint = Vector2.new(0, 1),
								BackgroundColor3 = Color3.fromRGB(0, 0, 0),
								BackgroundTransparency = 0.6,
								BorderSizePixel = 0,
								Visible = Computed(function()
									return isAttackOnCooldown:get()
								end)
							}
						}
					},

					-- 召喚怪物按鈕
					New "TextButton" {
						Name = "SpawnMonsterButton",
						Size = UDim2.new(0, 110, 0, 50),
						Position = UDim2.new(0, 130, 0, 0),
						BackgroundColor3 = Color3.fromRGB(100, 255, 100),
						BorderSizePixel = 2,
						BorderColor3 = Color3.fromRGB(255, 255, 255),
						Text = "👹 召喚怪物",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						Active = true,

						[OnEvent "Activated"] = function()
							print("🔥 Spawn monster button clicked!")
							self:_spawnMonster()
						end,

						[OnEvent "MouseButton1Click"] = function()
							print("🔥 Spawn monster button MouseButton1Click!")
							self:_spawnMonster()
						end
					}
				}
			}
		}
	}
end

-- 移除了快捷鍵輸入，只使用UI按鈕操作

-- 執行攻擊
function CombatController:_performAttack()
	print("🔥 _performAttack called")

	-- 檢查冷卻時間
	local currentTime = tick()
	if currentTime - self.lastAttackTime < self.attackCooldown then
		print("❌ Attack on cooldown")
		return
	end

	if not self.CombatService then
		self:_showMessage("戰鬥服務未就緒！", Color3.fromRGB(255, 255, 0))
		print("❌ CombatService not available")
		return
	end

	print("🔥 Current targetMonster:", self.targetMonster)
	if not self.targetMonster then
		-- 尋找最近的怪物
		self.targetMonster = self:_findNearestMonster()
		print("🔥 Found new target:", self.targetMonster)
	end

	if self.targetMonster then
		print("🎯 Attacking monster:", self.targetMonster)

		-- 開始冷卻
		self:_startAttackCooldown()

		-- 播放攻擊動畫
		self:_playPlayerAttackAnimation()

		-- 發送攻擊到服務器
		self.CombatService:AttackMonster(self.targetMonster)
	else
		self:_showMessage("沒有目標！", Color3.fromRGB(255, 255, 0))
		print("❌ No target found")
	end
end

-- 召喚怪物
function CombatController:_spawnMonster()
	if not self.MonsterService then
		self:_showMessage("服務未就緒！", Color3.fromRGB(255, 255, 0))
		return
	end

	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return
	end

	local playerPos = player.Character.HumanoidRootPart.Position
	local spawnPos = playerPos + Vector3.new(math.random(-10, 10), 0, math.random(-10, 10))

	self.MonsterService:SpawnMonster(nil, spawnPos)
end

-- 尋找最近的怪物（優化版本）
function CombatController:_findNearestMonster()
	if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
		return nil
	end

	local playerPos = player.Character.HumanoidRootPart.Position
	local nearestMonster, distance = TargetingSystem.findNearestMonster(playerPos, 30) -- 30格搜尋範圍

	-- 只在目標改變時輸出調試信息
	if nearestMonster ~= self.lastFoundTarget then
		if nearestMonster then
			print("🎯 New target selected:", nearestMonster, "distance:", math.floor(distance or 0))
		else
			print("🎯 No target found")
		end
		self.lastFoundTarget = nearestMonster
	end
	return nearestMonster
end

-- 開始攻擊冷卻
function CombatController:_startAttackCooldown()
	self.lastAttackTime = tick()
	isAttackOnCooldown:set(true)

	-- 啟動冷卻進度更新
	task.spawn(function()
		local startTime = self.lastAttackTime
		while tick() - startTime < self.attackCooldown do
			local elapsed = tick() - startTime
			local progress = 0.5 - (elapsed / self.attackCooldown)
			attackCooldownState:set(math.max(0, progress))
			task.wait(0.05) -- 20fps更新頻率
		end

		-- 冷卻結束
		isAttackOnCooldown:set(false)
		attackCooldownState:set(0)

		-- 播放冷卻完成音效
		self:_playSound("cooldown_ready")
	end)
end

-- 更新戰鬥邏輯（優化版本）
function CombatController:_updateCombat()
	-- 自動選擇目標
	if not self.targetMonster then
		self.targetMonster = self:_findNearestMonster()
	end

	-- 檢查目標是否仍然存在（使用優化的驗證）
	if self.targetMonster and not TargetingSystem.isMonsterValid(self.targetMonster) then
		self.targetMonster = nil
	end
end

-- 怪物生成事件
function CombatController:_onMonsterSpawned(instanceId, monsterId, position)
	self:_showMessage("怪物出現！", Color3.fromRGB(255, 100, 100))
	-- 播放召喚音效
	self:_playSound("spawn")
end

-- 怪物死亡事件
function CombatController:_onMonsterDied(instanceId)
	if self.targetMonster and self.targetMonster == instanceId then
		self.targetMonster = nil
	end
	self:_showMessage("怪物被擊敗！", Color3.fromRGB(100, 255, 100))
end

-- 怪物攻擊事件
function CombatController:_onMonsterAttack(instanceId, targetUserId)
	-- 只處理攻擊本地玩家的情況
	if targetUserId == player.UserId then
		-- 播放怪物攻擊動畫
		self:_playMonsterAttackAnimation(instanceId)
	end
end

-- 顯示傷害效果
function CombatController:_showDamageEffect(damage, source)
	self:_showMessage("-" .. damage, Color3.fromRGB(255, 100, 100))

	-- 紅色受傷畫面效果
	self:_showDamageScreen()

	-- 玩家受傷動畫
	self:_playPlayerHurtAnimation()

	-- 播放受傷音效
	self:_playSound("hurt")
end

-- 顯示攻擊效果
function CombatController:_showAttackEffect(monsterId, damage, killed)
	if killed then
		self:_showMessage("擊殺！+" .. damage, Color3.fromRGB(255, 255, 100))
		-- 擊殺特效
		self:_showKillEffect()
		-- 播放擊殺音效
		self:_playSound("kill")
	else
		self:_showMessage("+" .. damage, Color3.fromRGB(100, 255, 100))
	end

	-- 在怪物位置顯示傷害數字
	self:_showDamageNumber(monsterId, damage, killed)

	-- 玩家攻擊動畫
	self:_playPlayerAttackAnimation()

	-- 怪物受擊效果
	self:_showMonsterHitEffect(monsterId)

	-- 播放攻擊音效
	self:_playSound("attack")
end

-- 顯示消息
function CombatController:_showMessage(text, color)
	local message = New "TextLabel" {
		Size = UDim2.new(0, 200, 0, 50),
		Position = UDim2.new(0.5, -100, 0.3, 0),
		BackgroundTransparency = 1,
		Text = text,
		TextColor3 = color,
		TextScaled = true,
		Font = Enum.Font.GothamBold,
		Parent = combatUI
	}
	
	-- 動畫效果
	local TweenService = game:GetService("TweenService")
	local tween = TweenService:Create(
		message,
		TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Position = UDim2.new(0.5, -100, 0.2, 0),
			TextTransparency = 1
		}
	)
	
	tween:Play()
	tween.Completed:Connect(function()
		message:Destroy()
	end)
end

-- 紅色受傷畫面效果
function CombatController:_showDamageScreen()
	print("🔴 Creating damage screen overlay")
	local damageOverlay = New "Frame" {
		Name = "DamageOverlay",
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromRGB(255, 0, 0),
		BackgroundTransparency = 0.3, -- 更不透明，更明顯
		BorderSizePixel = 0,
		ZIndex = 1000,
		Parent = playerGui
	}
	print("🔴 Damage overlay created:", damageOverlay.Name)

	-- 淡出動畫
	local TweenService = game:GetService("TweenService")
	local fadeOut = TweenService:Create(
		damageOverlay,
		TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{BackgroundTransparency = 1}
	)

	fadeOut:Play()
	fadeOut.Completed:Connect(function()
		damageOverlay:Destroy()
	end)
end

-- 玩家受傷動畫
function CombatController:_playPlayerHurtAnimation()
	if not player.Character or not player.Character:FindFirstChild("Humanoid") then
		return
	end

	local humanoid = player.Character.Humanoid
	local rootPart = player.Character:FindFirstChild("HumanoidRootPart")

	if rootPart then
		-- 震動效果
		local TweenService = game:GetService("TweenService")
		local originalCFrame = rootPart.CFrame

		for i = 1, 3 do
			local shake = TweenService:Create(
				rootPart,
				TweenInfo.new(0.05, Enum.EasingStyle.Quad),
				{CFrame = originalCFrame * CFrame.new(math.random(-1, 1), 0, math.random(-1, 1))}
			)
			shake:Play()
			shake.Completed:Wait()
		end

		-- 恢復原位
		rootPart.CFrame = originalCFrame
	end
end

-- 玩家攻擊動畫
function CombatController:_playPlayerAttackAnimation()
	print("🎬 Playing sword attack animation")
	if not player.Character or not player.Character:FindFirstChild("Humanoid") then
		print("❌ Player character or humanoid not found")
		return
	end

	local humanoid = player.Character.Humanoid
	local rootPart = player.Character:FindFirstChild("HumanoidRootPart")
	local rightArm = player.Character:FindFirstChild("Right Arm") or player.Character:FindFirstChild("RightUpperArm")

	if rootPart then
		print("🎬 Player rootPart found, starting sword attack")
		local TweenService = game:GetService("TweenService")

		-- 確保玩家有劍
		local sword = self:_ensurePlayerSword()

		-- 找到目標怪物的位置
		local targetPosition = nil
		if self.targetMonster then
			for _, model in pairs(workspace:GetChildren()) do
				if model:IsA("Model") and string.find(model.Name, self.targetMonster) and model:FindFirstChild("HumanoidRootPart") then
					targetPosition = model.HumanoidRootPart.Position
					break
				end
			end
		end

		if targetPosition then
			print("🎯 Target position found:", targetPosition)
			-- 轉向目標
			local direction = (targetPosition - rootPart.Position).Unit
			local lookAtCFrame = CFrame.lookAt(rootPart.Position, targetPosition)

			-- 轉向動畫
			local turnTween = TweenService:Create(
				rootPart,
				TweenInfo.new(0.1, Enum.EasingStyle.Quad),
				{CFrame = lookAtCFrame}
			)
			turnTween:Play()

			turnTween.Completed:Connect(function()
				-- 揮劍動畫
				self:_performSwordSwing(sword, targetPosition)
			end)
		else
			print("❌ No target position, using default")
			-- 沒有目標時的通用揮劍
			local defaultTarget = rootPart.Position + rootPart.CFrame.LookVector * 5
			self:_performSwordSwing(sword, defaultTarget)
		end
	end
end

-- 確保玩家有劍
function CombatController:_ensurePlayerSword()
	if not player.Character then return nil end

	-- 檢查是否已經有劍
	local existingSword = player.Character:FindFirstChild("PlayerSword")
	if existingSword then
		return existingSword
	end

	-- 創建劍
	local sword = Instance.new("Part")
	sword.Name = "PlayerSword"
	sword.Size = Vector3.new(0.2, 4, 0.2)
	sword.Material = Enum.Material.Neon
	sword.BrickColor = BrickColor.new("Bright blue")
	sword.CanCollide = false
	sword.Parent = player.Character

	-- 劍柄
	local handle = Instance.new("Part")
	handle.Name = "Handle"
	handle.Size = Vector3.new(0.3, 1, 0.3)
	handle.Material = Enum.Material.Wood
	handle.BrickColor = BrickColor.new("Dark stone grey")
	handle.CanCollide = false
	handle.Parent = sword

	-- 連接劍身和劍柄（劍身在劍柄上方）
	local weld1 = Instance.new("WeldConstraint")
	weld1.Part0 = handle
	weld1.Part1 = sword
	weld1.Parent = handle

	-- 調整劍身位置：劍身在劍柄上方
	sword.CFrame = handle.CFrame * CFrame.new(0, 2.5, 0)

	-- 將劍附加到右手
	local rightHand = player.Character:FindFirstChild("Right Arm") or player.Character:FindFirstChild("RightHand")
	if rightHand then
		-- 使用 Motor6D 來更好地控制劍的位置
		local motor = Instance.new("Motor6D")
		motor.Name = "SwordMotor"
		motor.Part0 = rightHand
		motor.Part1 = handle
		motor.Parent = rightHand

		-- 正確的握劍位置：劍柄在手中，劍身向上
		motor.C1 = CFrame.new(0, -1, 0) * CFrame.Angles(math.rad(-90), 0, 0)

		-- 儲存 motor 引用以便攻擊時使用
		sword:SetAttribute("SwordMotorPath", rightHand.Name .. "/SwordMotor")
	end

	print("⚔️ Sword equipped!")
	return sword
end

-- 執行揮劍動作
function CombatController:_performSwordSwing(sword, targetPosition)
	print("🎬 _performSwordSwing called")
	if not sword or not player.Character then
		print("❌ No sword or character")
		return
	end

	local TweenService = game:GetService("TweenService")
	local rootPart = player.Character.HumanoidRootPart

	-- 嘗試多種可能的手臂名稱
	local rightArm = player.Character:FindFirstChild("Right Arm")
		or player.Character:FindFirstChild("RightHand")
		or player.Character:FindFirstChild("RightUpperArm")
		or player.Character:FindFirstChild("RightLowerArm")

	print("🔍 Right arm found:", rightArm and rightArm.Name or "nil")

	-- 獲取劍的 Motor6D
	local swordMotor = rightArm and rightArm:FindFirstChild("SwordMotor")
	print("🔍 Sword motor found:", swordMotor and "yes" or "no")

	if not swordMotor then
		print("❌ Sword motor not found, using simple animation")
		-- 如果沒有 Motor6D，使用簡單的特效動畫
		self:_createSimpleAttackEffects(targetPosition)
		return
	end

	print("⚔️ Starting sword swing animation")

	-- 儲存原始位置
	local originalC1 = swordMotor.C1

	-- 第一階段：舉劍準備（向後拉）
	local prepareCFrame = originalC1 * CFrame.Angles(math.rad(-45), 0, math.rad(30))
	local prepareTween = TweenService:Create(
		swordMotor,
		TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{C1 = prepareCFrame}
	)

	prepareTween:Play()
	prepareTween.Completed:Connect(function()
		-- 第二階段：快速揮劍（向前砍）
		local swingCFrame = originalC1 * CFrame.Angles(math.rad(45), 0, math.rad(-30))
		local swingTween = TweenService:Create(
			swordMotor,
			TweenInfo.new(0.15, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
			{C1 = swingCFrame}
		)

		swingTween:Play()

		-- 在揮劍開始時立即創建劍光效果
		self:_createSwordTrailEffect(sword, targetPosition)

		swingTween.Completed:Connect(function()
			-- 第三階段：回到原位
			local returnTween = TweenService:Create(
				swordMotor,
				TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
				{C1 = originalC1}
			)
			returnTween:Play()

			-- 創建攻擊特效
			self:_createSlashEffects(targetPosition)
		end)
	end)
end

-- 創建劍光軌跡效果
function CombatController:_createSwordTrailEffect(sword, targetPosition)
	if not sword or not sword:FindFirstChild("Handle") then return end

	local handle = sword:FindFirstChild("Handle")
	local swordTip = sword.CFrame.Position + sword.CFrame.UpVector * 2 -- 劍尖位置

	-- 創建劍光軌跡
	local trail = Instance.new("Part")
	trail.Name = "SwordTrail"
	trail.Size = Vector3.new(0.2, 0.2, 6)
	trail.Material = Enum.Material.Neon
	trail.BrickColor = BrickColor.new("Cyan")
	trail.CanCollide = false
	trail.Anchored = true
	trail.Transparency = 0.3
	trail.Parent = workspace

	-- 劍光從劍的位置掃向目標
	local startPos = swordTip
	local endPos = targetPosition + Vector3.new(0, 1, 0)

	trail.CFrame = CFrame.lookAt(startPos, endPos)

	-- 劍光掃過動畫
	local TweenService = game:GetService("TweenService")
	local trailTween = TweenService:Create(
		trail,
		TweenInfo.new(0.2, Enum.EasingStyle.Quad),
		{
			Size = Vector3.new(0.1, 0.1, 8),
			Transparency = 1
		}
	)

	trailTween:Play()
	trailTween.Completed:Connect(function()
		trail:Destroy()
	end)

	print("✨ Sword trail effect created")
end

-- 創建斬擊特效（使用對象池）
function CombatController:_createSlashEffects(targetPosition)
	-- 爆炸效果
	EffectPool.playEffect(EffectPool.TYPES.EXPLOSION, targetPosition, {
		BlastRadius = 8,
		BlastPressure = 0
	}, 2)

	-- 斬擊痕跡
	EffectPool.playEffect(EffectPool.TYPES.SLASH_MARK, targetPosition, {
		CFrame = CFrame.new(targetPosition) * CFrame.Angles(0, 0, math.rad(45))
	}, 1)

	print("💥 Slash effects created at target (using pool)")
end

-- 簡單的攻擊特效（備用方案，使用對象池）
function CombatController:_createSimpleAttackEffects(targetPosition)
	print("✨ Creating simple attack effects (using pool)")

	-- 創建劍光從玩家位置射向目標
	local playerPos = player.Character and player.Character.HumanoidRootPart.Position
	if not playerPos then return end

	local distance = (targetPosition - playerPos).Magnitude
	local midPoint = playerPos:Lerp(targetPosition, 0.5) + Vector3.new(0, 1, 0)

	-- 劍光束
	EffectPool.playEffect(EffectPool.TYPES.SWORD_BEAM, midPoint, {
		Size = Vector3.new(0.5, 0.5, distance),
		CFrame = CFrame.lookAt(playerPos + Vector3.new(0, 1, 0), targetPosition)
	}, 0.5)

	-- 目標位置爆炸
	EffectPool.playEffect(EffectPool.TYPES.EXPLOSION, targetPosition, {
		BlastRadius = 8,
		BlastPressure = 0
	}, 2)

	-- 斬擊痕跡
	EffectPool.playEffect(EffectPool.TYPES.SLASH_MARK, targetPosition, {
		CFrame = CFrame.new(targetPosition) * CFrame.Angles(0, 0, math.rad(45))
	}, 1)

	print("⚔️ Simple sword attack completed (using pool)!")
end

-- 擊殺特效
function CombatController:_showKillEffect()
	-- 創建擊殺特效
	local killEffect = New "Frame" {
		Name = "KillEffect",
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromRGB(255, 215, 0),
		BackgroundTransparency = 0.8,
		BorderSizePixel = 0,
		ZIndex = 999,
		Parent = playerGui
	}

	local TweenService = game:GetService("TweenService")

	-- 閃爍效果
	for i = 1, 3 do
		local flash = TweenService:Create(
			killEffect,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad),
			{BackgroundTransparency = 0.3}
		)
		flash:Play()
		flash.Completed:Wait()

		local fade = TweenService:Create(
			killEffect,
			TweenInfo.new(0.1, Enum.EasingStyle.Quad),
			{BackgroundTransparency = 0.8}
		)
		fade:Play()
		fade.Completed:Wait()
	end

	killEffect:Destroy()
end

-- 顯示傷害數字
function CombatController:_showDamageNumber(monsterId, damage, isKill)
	-- 尋找怪物模型
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and string.find(model.Name, monsterId) and model:FindFirstChild("HumanoidRootPart") then
			local rootPart = model.HumanoidRootPart

			-- 使用特效池創建傷害數字
			local damageGui = EffectPool.getEffect(EffectPool.TYPES.DAMAGE_NUMBER)
			if damageGui then
				damageGui.Adornee = rootPart
				damageGui.StudsOffset = Vector3.new(math.random(-1, 1), 3, math.random(-1, 1))
				damageGui.Parent = workspace

				local label = damageGui:FindFirstChild("TextLabel")
				if label then
					if isKill then
						label.Text = "KILL! " .. damage
						label.TextColor3 = Color3.fromRGB(255, 215, 0) -- 金色
					else
						label.Text = tostring(damage)
						label.TextColor3 = Color3.fromRGB(255, 100, 100) -- 紅色
					end

					-- 動畫效果
					local TweenService = game:GetService("TweenService")
					local floatTween = TweenService:Create(
						damageGui,
						TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
						{StudsOffset = damageGui.StudsOffset + Vector3.new(0, 3, 0)}
					)

					local fadeTween = TweenService:Create(
						label,
						TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
						{TextTransparency = 1}
					)

					floatTween:Play()
					fadeTween:Play()

					-- 自動回收
					task.spawn(function()
						task.wait(1.5)
						EffectPool.returnEffect(damageGui)
					end)
				end
			end
			break
		end
	end
end

-- 怪物受擊效果
function CombatController:_showMonsterHitEffect(monsterId)
	-- 尋找怪物模型
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and string.find(model.Name, monsterId) and model:FindFirstChild("HumanoidRootPart") then
			local rootPart = model.HumanoidRootPart

			-- 創建受擊特效
			EffectPool.playEffect(EffectPool.TYPES.EXPLOSION, rootPart.Position, {
				BlastRadius = 5,
				BlastPressure = 0
			}, 2)

			-- 怪物震動
			local TweenService = game:GetService("TweenService")
			local originalCFrame = rootPart.CFrame

			for _ = 1, 2 do
				local shake = TweenService:Create(
					rootPart,
					TweenInfo.new(0.05, Enum.EasingStyle.Quad),
					{CFrame = originalCFrame * CFrame.new(math.random(-0.5, 0.5), 0, math.random(-0.5, 0.5))}
				)
				shake:Play()
				shake.Completed:Wait()
			end

			rootPart.CFrame = originalCFrame
			break
		end
	end
end

-- 怪物攻擊動畫
function CombatController:_playMonsterAttackAnimation(instanceId)
	-- 尋找怪物模型
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and string.find(model.Name, instanceId) and model:FindFirstChild("HumanoidRootPart") then
			local rootPart = model.HumanoidRootPart
			local TweenService = game:GetService("TweenService")
			local originalCFrame = rootPart.CFrame

			-- 計算朝向玩家的方向
			if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
				local playerPos = player.Character.HumanoidRootPart.Position
				local direction = (playerPos - rootPart.Position).Unit
				local attackCFrame = rootPart.CFrame + direction * 2

				-- 攻擊衝刺
				local lunge = TweenService:Create(
					rootPart,
					TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
					{CFrame = attackCFrame}
				)

				lunge:Play()
				lunge.Completed:Connect(function()
					-- 回到原位
					local retreat = TweenService:Create(
						rootPart,
						TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
						{CFrame = originalCFrame}
					)
					retreat:Play()
				end)

				-- 創建攻擊特效
				local attackEffect = Instance.new("Explosion")
				attackEffect.Position = playerPos
				attackEffect.BlastRadius = 4
				attackEffect.BlastPressure = 0
				attackEffect.Parent = workspace
			end
			break
		end
	end
end

-- 播放音效
function CombatController:_playSound(soundType)
	-- 音效ID映射（這些是示例ID，實際使用時需要替換為真實的音效ID）
	local soundIds = {
		attack = "rbxasset://sounds/electronicpingshort.wav", -- 攻擊音效
		hurt = "rbxasset://sounds/impact_water.mp3", -- 受傷音效
		kill = "rbxasset://sounds/victory.wav", -- 擊殺音效
		spawn = "rbxasset://sounds/spawn.wav", -- 召喚音效
		cooldown_ready = "rbxasset://sounds/button.wav" -- 冷卻完成音效
	}

	local soundId = soundIds[soundType]
	if soundId then
		local sound = Instance.new("Sound")
		sound.SoundId = soundId
		sound.Volume = 0.5
		sound.Parent = workspace
		sound:Play()

		-- 播放完畢後清理
		sound.Ended:Connect(function()
			sound:Destroy()
		end)
	end
end

return CombatController

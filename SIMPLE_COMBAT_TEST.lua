--[[
    簡化的戰鬥測試
    在服務器控制台執行
]]

local Players = game:GetService("Players")

print("=== 簡化戰鬥測試 ===")

local player = Players:GetPlayers()[1]
if not player then
    print("❌ 沒有玩家在線")
    return
end

print("🧪 測試玩家:", player.Name)

-- 1. 確保玩家有寵物
local function ensurePet()
    local DataService = require(game.ServerScriptService.Services.DataService)
    local data = DataService:GetPlayerData(player)
    
    if not data.pets["bacterial_virus"] then
        data.pets["bacterial_virus"] = {
            id = "bacterial_virus",
            level = 1,
            experience = 0,
            isShiny = false,
            isActive = false,
            obtainedAt = os.time(),
        }
        DataService:SavePlayerData(player)
        print("✅ 已添加寵物")
    else
        print("✅ 玩家已有寵物")
    end
end

-- 2. 召喚寵物
local function summonPet()
    local PetService = require(game.ServerScriptService.Services.PetService)
    PetService:_recallPet(player)
    wait(0.5)
    PetService:_summonPet(player, "bacterial_virus")
    wait(1)
    
    -- 檢查寵物
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and (string.find(model.Name, "細菌病毒") or string.find(model.Name, "Bacterial")) then
            print("✅ 寵物已召喚:", model.Name)
            return model
        end
    end
    
    print("❌ 寵物召喚失敗")
    return nil
end

-- 3. 生成怪物
local function spawnMonster()
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 玩家角色不存在")
        return nil
    end
    
    -- 清理舊怪物
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "bacterial_virus_") then
            model:Destroy()
        end
    end
    
    wait(0.5)
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    local position = player.Character.HumanoidRootPart.Position + Vector3.new(8, 0, 0)
    
    MonsterService:SpawnMonster(player, "bacterial_virus", position)
    wait(1)
    
    -- 檢查怪物
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "bacterial_virus_") then
            print("✅ 怪物已生成:", model.Name)
            return model
        end
    end
    
    print("❌ 怪物生成失敗")
    return nil
end

-- 4. 檢查距離
local function checkDistance(petModel, monsterModel)
    if not petModel or not monsterModel then
        return false
    end
    
    local petRoot = petModel:FindFirstChild("HumanoidRootPart")
    local monsterRoot = monsterModel:FindFirstChild("HumanoidRootPart")
    
    if not petRoot or not monsterRoot then
        print("❌ 缺少 HumanoidRootPart")
        return false
    end
    
    local distance = (petRoot.Position - monsterRoot.Position).Magnitude
    print("📏 寵物到怪物距離:", distance, "格")
    
    if distance <= 15 then
        print("✅ 在戰鬥範圍內 (≤15格)")
        return true
    else
        print("❌ 超出戰鬥範圍 (>15格)")
        return false
    end
end

-- 5. 手動觸發寵物攻擊
local function manualPetAttack(petModel, monsterModel)
    if not petModel or not monsterModel then
        print("❌ 寵物或怪物不存在")
        return
    end
    
    -- 提取 instanceId
    local parts = string.split(monsterModel.Name, "_")
    if #parts < 2 then
        print("❌ 怪物名稱格式錯誤:", monsterModel.Name)
        return
    end
    
    local instanceId = parts[2]
    print("🎯 手動觸發寵物攻擊")
    print("   怪物 instanceId:", instanceId)
    
    -- 直接調用服務端攻擊
    local CombatService = require(game.ServerScriptService.Services.CombatService)
    CombatService:PetAttackMonster(player, "bacterial_virus", instanceId)
    
    print("✅ 攻擊命令已發送")
end

-- 執行測試
local function runTest()
    ensurePet()
    
    local petModel = summonPet()
    local monsterModel = spawnMonster()
    
    if petModel and monsterModel then
        local inRange = checkDistance(petModel, monsterModel)
        
        if inRange then
            print("\n🔥 手動測試寵物攻擊...")
            manualPetAttack(petModel, monsterModel)
            
            print("\n⏳ 等待5秒觀察效果...")
            for i = 1, 5 do
                wait(1)
                print("   等待中... " .. i .. "/5")
                
                -- 檢查怪物血量
                local humanoid = monsterModel:FindFirstChildOfClass("Humanoid")
                if humanoid then
                    print("   怪物血量:", humanoid.Health .. "/" .. humanoid.MaxHealth)
                end
            end
            
            print("\n💡 如果看到以下信息說明攻擊成功:")
            print("   - '🐾 Pet [petId] attacked monster [instanceId] for [damage] damage'")
            print("   - '☢️ Pet virus attack!'")
            print("   - 怪物血量減少")
            
        else
            print("❌ 寵物和怪物距離太遠，無法測試攻擊")
        end
    else
        print("❌ 寵物或怪物創建失敗")
    end
end

-- 運行測試
runTest()

print("\n=== 手動命令 ===")
print("-- 如果自動測試失敗，可以手動執行:")
print("local CombatService = require(game.ServerScriptService.Services.CombatService)")
print("local player = game.Players:GetPlayers()[1]")
print("-- 找到怪物的 instanceId (怪物名稱中 _ 後面的部分)")
print("local instanceId = 'YOUR_INSTANCE_ID'")
print("CombatService:PetAttackMonster(player, 'bacterial_virus', instanceId)")

print("\n-- 檢查活躍怪物:")
print("local MonsterService = require(game.ServerScriptService.Services.MonsterService)")
print("local monsters = MonsterService:GetActiveMonsters()")
print("for id, data in pairs(monsters) do")
print("    print('Monster:', id, data.monsterId)")
print("end")

print("\n=== 測試完成 ===")

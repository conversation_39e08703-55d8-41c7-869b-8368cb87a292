[package]
name = "your-username/pet-rpg"
version = "0.1.0"
realm = "shared"
registry = "https://github.com/UpliftGames/wally-index"

[dependencies]
promise = "evaera/promise@^4.0"
signal = "sleitnick/signal@^1.5"
tableutil = "sleitnick/table-util@^1.2"
knit = "sleitnick/knit@^1.5"
fusion = "elttob/fusion@^0.2"
janitor = "howmanysmall/janitor@^1.15"
zoneplus = "mattschrubb/zoneplus@3.2.0"

[server-dependencies]
profileservice = "firebird702/profileservice@1.1.0"

[client-dependencies]
flipper = "reselim/flipper@^1.1.0"

[dev-dependencies]
testez = "roblox/testez@^0.4"

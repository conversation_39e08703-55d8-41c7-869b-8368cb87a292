# 戰鬥系統設計文檔

## 概述
這是一個基於 Roblox 的多人在線戰鬥系統，支援玩家與怪物戰鬥、寵物系統和實時戰鬥動畫。

## 系統架構

### 服務端組件
- **CombatService**: 處理戰鬥邏輯、傷害計算、攻擊驗證
- **MonsterService**: 管理怪物生成、AI行為、生命週期
- **DataService**: 處理玩家數據、經驗值、金幣獎勵

### 客戶端組件
- **CombatController**: 處理戰鬥UI、動畫效果、用戶輸入
- **EffectController**: 管理視覺特效、音效播放

## 核心功能

### 戰鬥機制
- 玩家攻擊：點擊攻擊按鈕或自動攻擊最近怪物
- 寵物攻擊：寵物自動協助玩家戰鬥
- 怪物AI：追擊、攻擊、遊蕩行為
- 傷害計算：考慮攻擊力、防禦力的平衡系統

### 視覺效果
- 劍光軌跡：攻擊時的劍光效果
- 爆炸特效：命中時的視覺反饋
- 血量顯示：實時血量條更新
- 傷害數字：浮動傷害顯示

## 性能優化目標

### 當前問題
1. **頻繁的目標搜尋**: 每幀遍歷整個 workspace 尋找怪物
2. **AI更新開銷**: 所有怪物每幀都更新AI狀態
3. **特效創建開銷**: 頻繁創建和銷毀特效對象
4. **網絡通信冗餘**: 過多的客戶端-服務端通信

### 優化策略
1. **空間分割**: 使用區域系統減少搜尋範圍
2. **時間片輪詢**: 分散AI更新負載
3. **對象池**: 重用特效對象
4. **本地預測**: 提高響應性
5. **距離優化**: 根據距離調整更新頻率

## 技術規範

### 性能指標
- 目標FPS: 60fps
- 最大同時怪物數: 50隻
- 最大同時玩家數: 20人
- 網絡延遲容忍: <100ms

### 代碼標準
- 使用 Knit 框架進行服務管理
- 使用 Fusion 進行UI響應式更新
- 遵循 Roblox 性能最佳實踐
- 實施錯誤處理和日誌記錄

## 未來擴展

### 計劃功能
- 技能系統：特殊攻擊和法術
- 裝備系統：武器和護甲升級
- 公會戰：多人團隊戰鬥
- 地下城：PvE副本系統

### 技術改進
- 服務端權威驗證
- 反作弊系統
- 數據持久化優化
- 跨服務器通信

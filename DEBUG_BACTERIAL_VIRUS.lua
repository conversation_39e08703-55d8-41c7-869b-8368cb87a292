--[[
    調試 Bacterial Virus 寵物和怪物問題
    在服務器控制台執行這個腳本來診斷問題
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 獲取配置
local PetConfig = require(ReplicatedStorage.Shared.PetConfig)
local MonsterConfig = require(ReplicatedStorage.Shared.MonsterConfig)

print("=== Bacterial Virus 調試開始 ===")

-- 1. 檢查寵物配置
print("\n1. 檢查寵物配置:")
local petConfig = PetConfig.getPet("bacterial_virus")
if petConfig then
    print("✅ 寵物配置存在:", petConfig.name)
    print("   - 元素:", petConfig.element)
    print("   - 稀有度:", petConfig.rarity)
    print("   - 模型名稱:", petConfig.appearance.modelName)
else
    print("❌ 寵物配置不存在")
end

-- 2. 檢查怪物配置
print("\n2. 檢查怪物配置:")
local monsterConfig = MonsterConfig.getMonster("bacterial_virus")
if monsterConfig then
    print("✅ 怪物配置存在:", monsterConfig.name)
    print("   - 等級:", monsterConfig.level)
    print("   - 生命值:", monsterConfig.stats.maxHealth)
    print("   - 模型名稱:", monsterConfig.appearance.modelName)
else
    print("❌ 怪物配置不存在")
end

-- 3. 檢查模型是否存在
print("\n3. 檢查模型:")
local petFolder = workspace:FindFirstChild("Pet")
if petFolder then
    print("✅ Pet 文件夾存在")
    local bacterialModel = petFolder:FindFirstChild("Bacterial Virus")
    if bacterialModel then
        print("✅ Bacterial Virus 模型存在")
        print("   - 類型:", bacterialModel.ClassName)
        print("   - 子物件數量:", #bacterialModel:GetChildren())
        
        -- 檢查模型結構
        local parts = bacterialModel:GetChildren()
        print("   - 子物件:")
        for _, child in pairs(parts) do
            print("     *", child.Name, "(" .. child.ClassName .. ")")
        end
        
        -- 檢查是否有 PrimaryPart
        if bacterialModel.PrimaryPart then
            print("   - PrimaryPart:", bacterialModel.PrimaryPart.Name)
        else
            print("   - ⚠️ 沒有設置 PrimaryPart")
        end
    else
        print("❌ Bacterial Virus 模型不存在")
        print("   Pet 文件夾中的模型:")
        for _, child in pairs(petFolder:GetChildren()) do
            print("     *", child.Name)
        end
    end
else
    print("❌ Pet 文件夾不存在")
end

-- 4. 檢查怪物選擇邏輯
print("\n4. 檢查怪物選擇邏輯:")
local allMonsters = MonsterConfig.getAllMonsters()
print("所有怪物:")
for id, monster in pairs(allMonsters) do
    print("   -", id, "等級", monster.level)
end

-- 測試不同等級的怪物選擇
for level = 1, 10 do
    local selectedMonster = MonsterConfig.getMonsterByLevel(level)
    print("玩家等級", level, "選擇怪物:", selectedMonster)
end

-- 5. 檢查玩家數據
print("\n5. 檢查玩家數據:")
local function checkPlayerData(playerName)
    local player = Players:FindFirstChild(playerName)
    if player then
        local DataService = require(game.ServerScriptService.Services.DataService)
        local playerData = DataService:GetPlayerData(player)
        if playerData then
            print("玩家", playerName, "的寵物:")
            for petId, petData in pairs(playerData.pets or {}) do
                print("   -", petId, "等級", petData.level)
            end
            
            if playerData.pets and playerData.pets["bacterial_virus"] then
                print("✅ 玩家擁有 bacterial_virus 寵物")
            else
                print("❌ 玩家沒有 bacterial_virus 寵物")
            end
        else
            print("❌ 無法獲取玩家數據")
        end
    else
        print("❌ 找不到玩家:", playerName)
    end
end

-- 檢查第一個玩家的數據
local firstPlayer = Players:GetPlayers()[1]
if firstPlayer then
    checkPlayerData(firstPlayer.Name)
end

print("\n=== 調試完成 ===")

-- 6. 提供修復建議
print("\n修復建議:")
print("1. 如果模型不存在，請確保 Workspace.Pet.Bacterial Virus 存在")
print("2. 如果玩家沒有寵物，執行以下命令添加:")
print("   local player = game.Players:FindFirstChild('玩家名稱')")
print("   local DataService = require(game.ServerScriptService.Services.DataService)")
print("   local data = DataService:GetPlayerData(player)")
print("   data.pets['bacterial_virus'] = {level = 1, experience = 0}")
print("   DataService:SavePlayerData(player)")

print("3. 測試召喚寵物:")
print("   local PetService = require(game.ServerScriptService.Services.PetService)")
print("   PetService:_summonPet(player, 'bacterial_virus')")

print("4. 測試生成怪物:")
print("   local MonsterService = require(game.ServerScriptService.Services.MonsterService)")
print("   local pos = Vector3.new(0, 10, 0)")
print("   MonsterService:SpawnMonster(player, 'bacterial_virus', pos)")

print("\n=== 快速修復命令 ===")
print("1. 給當前第一個玩家添加寵物:")
print("   local player = game.Players:GetPlayers()[1]")
print("   if player then")
print("       local DataService = require(game.ServerScriptService.Services.DataService)")
print("       local data = DataService:GetPlayerData(player)")
print("       data.pets['bacterial_virus'] = {id='bacterial_virus', level=1, experience=0, isShiny=false, isActive=false, obtainedAt=os.time()}")
print("       table.insert(data.petDex.discovered, 'bacterial_virus')")
print("       DataService:SavePlayerData(player)")
print("       print('✅ 寵物已添加')")
print("   end")

print("\n2. 測試寵物召喚:")
print("   local PetService = require(game.ServerScriptService.Services.PetService)")
print("   PetService:_summonPet(player, 'bacterial_virus')")

print("\n3. 強制生成 bacterial_virus 怪物:")
print("   local MonsterService = require(game.ServerScriptService.Services.MonsterService)")
print("   local player = game.Players:GetPlayers()[1]")
print("   if player and player.Character then")
print("       local pos = player.Character.HumanoidRootPart.Position + Vector3.new(10, 0, 0)")
print("       MonsterService:SpawnMonster(player, 'bacterial_virus', pos)")
print("   end")

print("\n4. 檢查模型結構:")
print("   local model = workspace.Pet:FindFirstChild('Bacterial Virus')")
print("   if model then")
print("       print('模型存在，子物件:')")
print("       for _, child in pairs(model:GetChildren()) do")
print("           print('  -', child.Name, child.ClassName)")
print("       end")
print("   else")
print("       print('❌ 模型不存在')")
print("   end")

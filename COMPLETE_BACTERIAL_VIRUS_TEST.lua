--[[
    完整的 Bacterial Virus 測試
    在服務器控制台執行這個腳本來測試所有功能
]]

local Players = game:GetService("Players")

print("=== 完整 Bacterial Virus 測試 ===")

local player = Players:GetPlayers()[1]
if not player then
    print("❌ 沒有玩家在線")
    return
end

print("🧪 測試玩家:", player.Name)

-- 1. 確保玩家有寵物
local function ensurePetOwnership()
    print("\n1. 確保寵物擁有權...")
    
    local DataService = require(game.ServerScriptService.Services.DataService)
    local playerData = DataService:GetPlayerData(player)
    
    if not playerData.pets["bacterial_virus"] then
        playerData.pets["bacterial_virus"] = {
            id = "bacterial_virus",
            level = 1,
            experience = 0,
            isShiny = false,
            isActive = false,
            obtainedAt = os.time(),
        }
        
        if not table.find(playerData.petDex.discovered, "bacterial_virus") then
            table.insert(playerData.petDex.discovered, "bacterial_virus")
        end
        
        DataService:SavePlayerData(player)
        print("✅ 寵物已添加")
    else
        print("✅ 玩家已擁有寵物")
    end
end

-- 2. 清理環境
local function cleanupEnvironment()
    print("\n2. 清理環境...")
    
    -- 清理舊怪物
    local monsterCount = 0
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "bacterial_virus_") then
            model:Destroy()
            monsterCount = monsterCount + 1
        end
    end
    
    -- 清理舊寵物
    local petCount = 0
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and (string.find(model.Name, "細菌病毒") or string.find(model.Name, "Bacterial")) then
            model:Destroy()
            petCount = petCount + 1
        end
    end
    
    print("🧹 清理了", monsterCount, "個怪物和", petCount, "個寵物")
    wait(0.5)
end

-- 3. 召喚寵物
local function summonPet()
    print("\n3. 召喚寵物...")
    
    local PetService = require(game.ServerScriptService.Services.PetService)
    PetService:_summonPet(player, "bacterial_virus")
    wait(2)
    
    -- 檢查寵物
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and (string.find(model.Name, "細菌病毒") or string.find(model.Name, "Bacterial")) then
            print("✅ 寵物召喚成功:", model.Name)
            return model
        end
    end
    
    print("❌ 寵物召喚失敗")
    return nil
end

-- 4. 生成怪物
local function spawnMonster()
    print("\n4. 生成怪物...")
    
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        print("❌ 玩家角色不存在")
        return nil
    end
    
    local MonsterService = require(game.ServerScriptService.Services.MonsterService)
    local position = player.Character.HumanoidRootPart.Position + Vector3.new(8, 0, 0)
    
    print("🎯 生成位置:", position)
    MonsterService:SpawnMonster(player, "bacterial_virus", position)
    wait(2)
    
    -- 檢查怪物
    for _, model in pairs(workspace:GetChildren()) do
        if model:IsA("Model") and string.find(model.Name, "bacterial_virus_") then
            print("✅ 怪物生成成功:", model.Name)
            
            -- 檢查怪物結構
            local humanoid = model:FindFirstChildOfClass("Humanoid")
            local rootPart = model:FindFirstChild("HumanoidRootPart")
            
            if humanoid then
                print("   血量:", humanoid.Health .. "/" .. humanoid.MaxHealth)
            end
            if rootPart then
                print("   位置:", rootPart.Position)
            end
            
            return model
        end
    end
    
    print("❌ 怪物生成失敗")
    return nil
end

-- 5. 測試主角攻擊
local function testPlayerAttack(monsterModel)
    print("\n5. 測試主角攻擊...")
    
    if not monsterModel then
        print("❌ 沒有怪物可攻擊")
        return false
    end
    
    -- 提取 instanceId
    local parts = string.split(monsterModel.Name, "_")
    if #parts < 2 then
        print("❌ 怪物名稱格式錯誤")
        return false
    end
    
    local instanceId = parts[2]
    print("🗡️ 主角攻擊怪物 instanceId:", instanceId)
    
    -- 記錄攻擊前血量
    local humanoid = monsterModel:FindFirstChildOfClass("Humanoid")
    local beforeHealth = humanoid and humanoid.Health or 0
    print("   攻擊前血量:", beforeHealth)
    
    -- 執行攻擊
    local CombatService = require(game.ServerScriptService.Services.CombatService)
    CombatService:AttackMonster(player, instanceId)
    
    wait(1)
    
    -- 檢查攻擊後血量
    humanoid = monsterModel:FindFirstChildOfClass("Humanoid")
    local afterHealth = humanoid and humanoid.Health or 0
    print("   攻擊後血量:", afterHealth)
    
    if afterHealth < beforeHealth then
        print("✅ 主角攻擊成功，造成", beforeHealth - afterHealth, "點傷害")
        return true
    else
        print("❌ 主角攻擊無效")
        return false
    end
end

-- 6. 測試寵物攻擊
local function testPetAttack(petModel, monsterModel)
    print("\n6. 測試寵物攻擊...")
    
    if not petModel or not monsterModel then
        print("❌ 寵物或怪物不存在")
        return false
    end
    
    -- 提取 instanceId
    local parts = string.split(monsterModel.Name, "_")
    local instanceId = parts[2]
    print("🐾 寵物攻擊怪物 instanceId:", instanceId)
    
    -- 記錄攻擊前血量
    local humanoid = monsterModel:FindFirstChildOfClass("Humanoid")
    local beforeHealth = humanoid and humanoid.Health or 0
    print("   攻擊前血量:", beforeHealth)
    
    -- 執行攻擊
    local CombatService = require(game.ServerScriptService.Services.CombatService)
    CombatService:PetAttackMonster(player, "bacterial_virus", instanceId)
    
    wait(1)
    
    -- 檢查攻擊後血量
    humanoid = monsterModel:FindFirstChildOfClass("Humanoid")
    local afterHealth = humanoid and humanoid.Health or 0
    print("   攻擊後血量:", afterHealth)
    
    if afterHealth < beforeHealth then
        print("✅ 寵物攻擊成功，造成", beforeHealth - afterHealth, "點傷害")
        return true
    else
        print("❌ 寵物攻擊無效")
        return false
    end
end

-- 7. 測試自動戰鬥
local function testAutoCombat(petModel, monsterModel)
    print("\n7. 測試自動戰鬥...")
    
    if not petModel or not monsterModel then
        print("❌ 寵物或怪物不存在")
        return
    end
    
    print("⏳ 等待10秒觀察寵物自動攻擊...")
    
    local humanoid = monsterModel:FindFirstChildOfClass("Humanoid")
    local initialHealth = humanoid and humanoid.Health or 0
    print("   初始血量:", initialHealth)
    
    for i = 1, 10 do
        wait(1)
        print("   等待中... " .. i .. "/10")
        
        -- 檢查怪物是否還存在
        if not monsterModel.Parent then
            print("💀 怪物已被擊殺!")
            return true
        end
        
        -- 檢查血量變化
        humanoid = monsterModel:FindFirstChildOfClass("Humanoid")
        local currentHealth = humanoid and humanoid.Health or 0
        if currentHealth < initialHealth then
            print("✅ 檢測到自動攻擊! 血量:", currentHealth .. "/" .. humanoid.MaxHealth)
            initialHealth = currentHealth
        end
    end
    
    print("💡 自動戰鬥測試完成")
end

-- 執行完整測試
local function runCompleteTest()
    ensurePetOwnership()
    cleanupEnvironment()
    
    local petModel = summonPet()
    local monsterModel = spawnMonster()
    
    if petModel and monsterModel then
        print("\n🎯 基本功能測試成功")
        
        local playerAttackSuccess = testPlayerAttack(monsterModel)
        local petAttackSuccess = testPetAttack(petModel, monsterModel)
        
        if playerAttackSuccess and petAttackSuccess then
            print("\n✅ 手動攻擊測試成功")
            testAutoCombat(petModel, monsterModel)
        else
            print("\n❌ 手動攻擊測試失敗")
            if not playerAttackSuccess then
                print("   - 主角攻擊失敗")
            end
            if not petAttackSuccess then
                print("   - 寵物攻擊失敗")
            end
        end
        
        print("\n=== 測試完成 ===")
        print("💡 請觀察:")
        print("   1. 寵物是否在玩家身邊")
        print("   2. 怪物是否在玩家右側")
        print("   3. 攻擊是否造成傷害")
        print("   4. 是否有攻擊特效")
        print("   5. 寵物是否會自動攻擊")
        
    else
        print("\n❌ 基本功能測試失敗")
        if not petModel then
            print("   - 寵物召喚失敗")
        end
        if not monsterModel then
            print("   - 怪物生成失敗")
        end
    end
end

-- 運行完整測試
runCompleteTest()

print("\n=== 調試信息 ===")
print("如果測試失敗，請檢查控制台是否有以下信息:")
print("   - '👹 Created monster from preset model'")
print("   - '🩸 DamageMonster called'")
print("   - '👹 Monster X took Y damage'")
print("   - '⚔️ Player X attacked monster'")
print("   - '🐾 Pet X attacked monster'")
print("   - '☢️ Pet virus attack!'")

print("\n=== 完整測試結束 ===")
